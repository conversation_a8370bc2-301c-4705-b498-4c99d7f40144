"""
语义理解和智能推荐引擎

实现业务逻辑理解、设计模式识别、代码意图推断和智能代码补全功能。
"""

import ast
import json
import re
import time
from collections import defaultdict
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

from ..core.interfaces import ToolDefinition
from ..core.types import ConfigDict
from ..storage.unified_manager import UnifiedDataManager
from .base import (
    BaseTool,
    ExecutionMetadata,
    ResourceUsage,
    ToolExecutionRequest,
    ToolExecutionResult,
)


class BaseSemanticTool(BaseTool):
    """语义引擎工具基类"""

    def __init__(self, config: Optional[ConfigDict] = None):
        super().__init__(config)
        self.data_manager = UnifiedDataManager(
            self.config.get("chromadb_path", "./mcp_unified_db")
        )
        self.supported_languages = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.go': 'go',
            '.rs': 'rust'
        }

    def _get_file_language(self, file_path: str) -> str:
        """获取文件语言类型"""
        ext = Path(file_path).suffix.lower()
        return self.supported_languages.get(ext, 'unknown')

    def _read_file_safely(self, file_path: str) -> Optional[str]:
        """安全读取文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"读取文件失败 {file_path}: {e}")
            return None

    def _find_files_by_pattern(self, directory: str, patterns: List[str]) -> List[str]:
        """根据模式查找文件"""
        files = []
        for pattern in patterns:
            if pattern.startswith('*.'):
                ext = pattern[1:]
                for file_path in Path(directory).rglob(f"*{ext}"):
                    files.append(str(file_path))
        return files

    def _collect_target_files(self, target: Dict[str, Any]) -> List[str]:
        """收集目标文件"""
        target_type = target.get("type", "directory")
        path = target.get("path", ".")
        patterns = target.get("patterns", ["*.py", "*.js", "*.ts"])

        files = []
        
        if target_type == "file":
            if Path(path).is_file():
                files.append(path)
        elif target_type == "directory":
            files = self._find_files_by_pattern(path, patterns)
        elif target_type == "project":
            common_dirs = ["src", "lib", "app", "components", "modules"]
            for dir_name in common_dirs:
                dir_path = Path(path) / dir_name
                if dir_path.is_dir():
                    files.extend(self._find_files_by_pattern(str(dir_path), patterns))
            
            if not files:
                files = self._find_files_by_pattern(path, patterns)

        return files


class SemanticUnderstanding(BaseSemanticTool):
    """语义理解引擎"""

    def get_definition(self) -> ToolDefinition:
        return ToolDefinition(
            name="understand_semantics",
            description="理解代码的业务逻辑、设计模式和架构意图",
            parameters={
                "type": "object",
                "properties": {
                    "target": {
                        "type": "object",
                        "properties": {
                            "type": {
                                "type": "string",
                                "enum": ["file", "directory", "function", "class"],
                                "description": "分析目标类型",
                                "default": "directory",
                            },
                            "path": {
                                "type": "string",
                                "description": "目标路径",
                                "default": ".",
                            },
                            "patterns": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "文件匹配模式",
                                "default": ["*.py", "*.js", "*.ts"],
                            },
                        },
                        "description": "分析目标配置",
                    },
                    "understanding_types": {
                        "type": "array",
                        "items": {
                            "type": "string",
                            "enum": ["business_logic", "design_patterns", "code_intent", "architecture"]
                        },
                        "description": "理解类型",
                        "default": ["business_logic", "design_patterns", "code_intent"],
                    },
                    "analysis_depth": {
                        "type": "string",
                        "enum": ["shallow", "medium", "deep"],
                        "description": "分析深度",
                        "default": "medium",
                    },
                    "include_suggestions": {
                        "type": "boolean",
                        "description": "是否包含改进建议",
                        "default": True,
                    },
                },
                "required": [],
            },
        )

    async def execute(self, request: ToolExecutionRequest) -> ToolExecutionResult:
        """执行语义理解"""
        start_time = time.time()
        params = request.parameters

        try:
            target = params.get("target", {"type": "directory", "path": "."})
            understanding_types = params.get("understanding_types", ["business_logic", "design_patterns", "code_intent"])
            analysis_depth = params.get("analysis_depth", "medium")
            include_suggestions = params.get("include_suggestions", True)

            # 收集目标文件
            target_files = self._collect_target_files(target)
            
            if not target_files:
                return self._create_error_result("NO_FILES", "未找到符合条件的文件")

            # 执行语义理解分析
            understanding_results = {}
            
            if "business_logic" in understanding_types:
                understanding_results["business_logic"] = self._analyze_business_logic(target_files, analysis_depth)
            
            if "design_patterns" in understanding_types:
                understanding_results["design_patterns"] = self._identify_design_patterns(target_files, analysis_depth)
            
            if "code_intent" in understanding_types:
                understanding_results["code_intent"] = self._infer_code_intent(target_files, analysis_depth)
            
            if "architecture" in understanding_types:
                understanding_results["architecture"] = self._analyze_architecture_patterns(target_files, analysis_depth)

            # 生成理解报告
            understanding_report = {
                "target": target,
                "understanding_types": understanding_types,
                "analysis_depth": analysis_depth,
                "files_analyzed": len(target_files),
                "understanding_results": understanding_results,
                "summary": self._generate_understanding_summary(understanding_results),
                "insights": self._generate_semantic_insights(understanding_results),
                "suggestions": self._generate_improvement_suggestions(understanding_results) if include_suggestions else None,
                "timestamp": time.time()
            }

            # 存储理解结果
            self._store_understanding_result("semantic_understanding", understanding_report)

            # 创建执行元数据
            metadata = ExecutionMetadata(
                execution_time=(time.time() - start_time) * 1000,
                memory_used=len(str(understanding_report)) / 1024 / 1024,
                cpu_time=(time.time() - start_time) * 1000,
                io_operations=len(target_files),
            )

            resources = ResourceUsage(
                memory_mb=len(str(understanding_report)) / 1024 / 1024,
                cpu_time_ms=(time.time() - start_time) * 1000,
                io_operations=len(target_files),
            )

            return self._create_success_result(understanding_report, metadata, resources)

        except Exception as e:
            print(f"语义理解执行异常: {e}")
            return self._create_error_result("EXECUTION_ERROR", f"执行异常: {str(e)}")

    def _analyze_business_logic(self, files: List[str], depth: str) -> Dict[str, Any]:
        """分析业务逻辑"""
        business_logic = {
            "domains": [],
            "entities": [],
            "operations": [],
            "workflows": [],
            "business_rules": []
        }
        
        for file_path in files:
            content = self._read_file_safely(file_path)
            if not content:
                continue
            
            language = self._get_file_language(file_path)
            file_logic = self._extract_business_logic_from_file(content, language, file_path, depth)
            
            # 合并结果
            for key in business_logic:
                business_logic[key].extend(file_logic.get(key, []))
        
        # 分析业务域
        business_logic["domain_analysis"] = self._analyze_business_domains(business_logic)
        
        return business_logic

    def _extract_business_logic_from_file(self, content: str, language: str, file_path: str, depth: str) -> Dict[str, Any]:
        """从文件中提取业务逻辑"""
        logic = {
            "domains": [],
            "entities": [],
            "operations": [],
            "workflows": [],
            "business_rules": []
        }
        
        if language == "python":
            logic = self._extract_python_business_logic(content, file_path, depth)
        elif language in ["javascript", "typescript"]:
            logic = self._extract_js_business_logic(content, file_path, depth)
        
        return logic

    def _extract_python_business_logic(self, content: str, file_path: str, depth: str) -> Dict[str, Any]:
        """提取Python业务逻辑"""
        logic = {
            "domains": [],
            "entities": [],
            "operations": [],
            "workflows": [],
            "business_rules": []
        }
        
        try:
            tree = ast.parse(content)
            
            # 分析类（可能是实体）
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    entity_info = self._analyze_class_as_entity(node, file_path)
                    if entity_info:
                        logic["entities"].append(entity_info)
                
                elif isinstance(node, ast.FunctionDef):
                    # 分析函数（可能是操作或业务规则）
                    func_info = self._analyze_function_business_logic(node, file_path)
                    if func_info["type"] == "operation":
                        logic["operations"].append(func_info)
                    elif func_info["type"] == "business_rule":
                        logic["business_rules"].append(func_info)
            
            # 分析工作流（基于函数调用序列）
            workflows = self._extract_workflows_from_ast(tree, file_path)
            logic["workflows"].extend(workflows)
            
            # 推断业务域
            domain = self._infer_domain_from_file(file_path, content)
            if domain:
                logic["domains"].append(domain)
        
        except SyntaxError:
            pass
        
        return logic

    def _analyze_class_as_entity(self, class_node: ast.ClassDef, file_path: str) -> Optional[Dict[str, Any]]:
        """分析类作为业务实体"""
        # 检查是否像业务实体
        entity_indicators = ["model", "entity", "data", "user", "order", "product", "customer"]
        class_name_lower = class_node.name.lower()
        
        if any(indicator in class_name_lower for indicator in entity_indicators):
            # 提取属性
            attributes = []
            methods = []
            
            for node in class_node.body:
                if isinstance(node, ast.FunctionDef):
                    if node.name.startswith('__'):
                        continue  # 跳过魔法方法
                    methods.append({
                        "name": node.name,
                        "line": node.lineno,
                        "is_property": self._is_property_method(node)
                    })
                elif isinstance(node, ast.Assign):
                    # 类属性
                    for target in node.targets:
                        if isinstance(target, ast.Name):
                            attributes.append({
                                "name": target.id,
                                "line": node.lineno,
                                "type": "class_attribute"
                            })
            
            return {
                "name": class_node.name,
                "file": file_path,
                "line": class_node.lineno,
                "type": "entity",
                "attributes": attributes,
                "methods": methods,
                "business_relevance": self._calculate_business_relevance(class_node.name, attributes, methods)
            }
        
        return None

    def _is_property_method(self, func_node: ast.FunctionDef) -> bool:
        """判断是否为属性方法"""
        # 检查装饰器
        for decorator in func_node.decorator_list:
            if isinstance(decorator, ast.Name) and decorator.id == "property":
                return True
        return False

    def _analyze_function_business_logic(self, func_node: ast.FunctionDef, file_path: str) -> Dict[str, Any]:
        """分析函数的业务逻辑"""
        func_name_lower = func_node.name.lower()
        
        # 操作关键词
        operation_keywords = ["create", "update", "delete", "process", "calculate", "validate", "send", "get", "find"]
        # 业务规则关键词
        rule_keywords = ["check", "verify", "validate", "ensure", "require", "allow", "deny"]
        
        func_type = "operation"
        if any(keyword in func_name_lower for keyword in rule_keywords):
            func_type = "business_rule"
        elif any(keyword in func_name_lower for keyword in operation_keywords):
            func_type = "operation"
        
        # 分析函数复杂度和业务相关性
        complexity = self._calculate_function_complexity(func_node)
        business_score = self._calculate_business_score(func_node.name, func_node)
        
        return {
            "name": func_node.name,
            "file": file_path,
            "line": func_node.lineno,
            "type": func_type,
            "complexity": complexity,
            "business_score": business_score,
            "parameters": [arg.arg for arg in func_node.args.args],
            "docstring": ast.get_docstring(func_node)
        }

    def _calculate_function_complexity(self, func_node: ast.FunctionDef) -> int:
        """计算函数复杂度"""
        complexity = 1
        for node in ast.walk(func_node):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(node, ast.ExceptHandler):
                complexity += 1
        return complexity

    def _calculate_business_score(self, name: str, func_node: ast.FunctionDef) -> float:
        """计算业务相关性分数"""
        score = 0.0
        name_lower = name.lower()
        
        # 基于名称的业务关键词
        business_keywords = [
            "user", "customer", "order", "product", "payment", "invoice", "account",
            "login", "register", "purchase", "sale", "inventory", "report", "analytics"
        ]
        
        for keyword in business_keywords:
            if keyword in name_lower:
                score += 0.3
        
        # 基于文档字符串
        docstring = ast.get_docstring(func_node)
        if docstring:
            doc_lower = docstring.lower()
            for keyword in business_keywords:
                if keyword in doc_lower:
                    score += 0.1
        
        return min(score, 1.0)

    def _calculate_business_relevance(self, name: str, attributes: List[Dict], methods: List[Dict]) -> float:
        """计算业务相关性"""
        score = 0.0
        
        # 基于类名
        business_class_names = ["user", "customer", "order", "product", "payment", "account"]
        name_lower = name.lower()
        for biz_name in business_class_names:
            if biz_name in name_lower:
                score += 0.4
        
        # 基于属性名
        business_attr_names = ["id", "name", "email", "price", "quantity", "status", "date"]
        for attr in attributes:
            attr_lower = attr["name"].lower()
            for biz_attr in business_attr_names:
                if biz_attr in attr_lower:
                    score += 0.1
        
        return min(score, 1.0)

    def _extract_workflows_from_ast(self, tree: ast.AST, file_path: str) -> List[Dict[str, Any]]:
        """从AST提取工作流"""
        workflows = []
        
        # 查找函数中的调用序列
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                call_sequence = []
                for child in ast.walk(node):
                    if isinstance(child, ast.Call):
                        call_info = self._extract_call_info(child)
                        if call_info:
                            call_sequence.append(call_info)
                
                if len(call_sequence) >= 3:  # 至少3个步骤才算工作流
                    workflows.append({
                        "name": f"{node.name}_workflow",
                        "function": node.name,
                        "file": file_path,
                        "line": node.lineno,
                        "steps": call_sequence,
                        "complexity": len(call_sequence)
                    })
        
        return workflows

    def _extract_call_info(self, call_node: ast.Call) -> Optional[Dict[str, Any]]:
        """提取调用信息"""
        if isinstance(call_node.func, ast.Name):
            return {
                "type": "function_call",
                "name": call_node.func.id,
                "line": call_node.lineno
            }
        elif isinstance(call_node.func, ast.Attribute):
            return {
                "type": "method_call",
                "name": call_node.func.attr,
                "object": self._get_object_name(call_node.func.value),
                "line": call_node.lineno
            }
        return None

    def _get_object_name(self, node: ast.AST) -> str:
        """获取对象名称"""
        if isinstance(node, ast.Name):
            return node.id
        elif isinstance(node, ast.Attribute):
            return f"{self._get_object_name(node.value)}.{node.attr}"
        else:
            return "unknown"

    def _infer_domain_from_file(self, file_path: str, content: str) -> Optional[Dict[str, Any]]:
        """从文件推断业务域"""
        file_name = Path(file_path).stem.lower()
        content_lower = content.lower()
        
        # 业务域关键词映射
        domain_keywords = {
            "user_management": ["user", "auth", "login", "register", "profile"],
            "e_commerce": ["product", "order", "cart", "payment", "checkout"],
            "financial": ["payment", "invoice", "billing", "transaction", "account"],
            "content_management": ["article", "post", "content", "media", "cms"],
            "analytics": ["report", "analytics", "metrics", "dashboard", "stats"]
        }
        
        for domain, keywords in domain_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in file_name:
                    score += 2
                if keyword in content_lower:
                    score += 1
            
            if score >= 3:
                return {
                    "name": domain,
                    "file": file_path,
                    "confidence": min(score / 10, 1.0),
                    "keywords_found": [kw for kw in keywords if kw in file_name or kw in content_lower]
                }
        
        return None

    def _extract_js_business_logic(self, content: str, file_path: str, depth: str) -> Dict[str, Any]:
        """提取JavaScript业务逻辑（简化实现）"""
        logic = {
            "domains": [],
            "entities": [],
            "operations": [],
            "workflows": [],
            "business_rules": []
        }
        
        # 简化的JS分析
        lines = content.split('\n')
        
        # 查找类或对象定义
        for line_num, line in enumerate(lines, 1):
            # 类定义
            class_match = re.search(r'class\s+(\w+)', line)
            if class_match:
                class_name = class_match.group(1)
                if self._is_business_entity_name(class_name):
                    logic["entities"].append({
                        "name": class_name,
                        "file": file_path,
                        "line": line_num,
                        "type": "entity",
                        "language": "javascript"
                    })
            
            # 函数定义
            func_match = re.search(r'(?:function\s+(\w+)|(\w+)\s*[:=]\s*(?:function|\([^)]*\)\s*=>))', line)
            if func_match:
                func_name = func_match.group(1) or func_match.group(2)
                if self._is_business_operation_name(func_name):
                    logic["operations"].append({
                        "name": func_name,
                        "file": file_path,
                        "line": line_num,
                        "type": "operation",
                        "language": "javascript"
                    })
        
        return logic

    def _is_business_entity_name(self, name: str) -> bool:
        """判断是否为业务实体名称"""
        business_entities = ["user", "customer", "order", "product", "payment", "account", "invoice"]
        name_lower = name.lower()
        return any(entity in name_lower for entity in business_entities)

    def _is_business_operation_name(self, name: str) -> bool:
        """判断是否为业务操作名称"""
        business_operations = ["create", "update", "delete", "process", "calculate", "validate", "send", "get"]
        name_lower = name.lower()
        return any(op in name_lower for op in business_operations)

    def _analyze_business_domains(self, business_logic: Dict[str, Any]) -> Dict[str, Any]:
        """分析业务域"""
        domains = business_logic.get("domains", [])
        entities = business_logic.get("entities", [])
        operations = business_logic.get("operations", [])
        
        # 统计域分布
        domain_stats = defaultdict(int)
        for domain in domains:
            domain_stats[domain["name"]] += 1
        
        # 分析实体-操作关系
        entity_operation_map = defaultdict(list)
        for operation in operations:
            op_name_lower = operation["name"].lower()
            for entity in entities:
                entity_name_lower = entity["name"].lower()
                if entity_name_lower in op_name_lower:
                    entity_operation_map[entity["name"]].append(operation["name"])
        
        return {
            "domain_distribution": dict(domain_stats),
            "total_domains": len(domain_stats),
            "entity_operation_mapping": dict(entity_operation_map),
            "domain_complexity": self._calculate_domain_complexity(domains, entities, operations)
        }

    def _calculate_domain_complexity(self, domains: List[Dict], entities: List[Dict], operations: List[Dict]) -> Dict[str, Any]:
        """计算域复杂度"""
        return {
            "entity_count": len(entities),
            "operation_count": len(operations),
            "avg_operations_per_entity": len(operations) / max(len(entities), 1),
            "complexity_score": (len(entities) * 0.3 + len(operations) * 0.7) / 10
        }

    async def cleanup(self) -> None:
        """清理资源"""
        pass
