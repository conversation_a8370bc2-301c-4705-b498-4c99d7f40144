"""
轻量化版本管理系统

为 Agent 提供撤销更改、版本回滚等能力，无需完整的 Git 功能，
专注于 Agent 操作的可逆性和安全性。
"""

import hashlib
import json
import os
import shutil
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from ..core.interfaces import ToolDefinition
from ..core.types import ConfigDict
from ..storage.unified_manager import UnifiedDataManager
from .base import (
    BaseTool,
    ExecutionMetadata,
    ResourceUsage,
    ToolExecutionRequest,
    ToolExecutionResult,
)


class BaseVersionTool(BaseTool):
    """版本管理工具基类"""

    def __init__(self, config: Optional[ConfigDict] = None):
        super().__init__(config)
        self.data_manager = UnifiedDataManager(
            self.config.get("chromadb_path", "./mcp_unified_db")
        )
        self.backup_dir = self.config.get("backup_directory", "./mcp_backups")
        self.max_history_size = self.config.get("max_history_size", 1000)
        self.auto_cleanup_days = self.config.get("auto_cleanup_days", 7)
        
        # 确保备份目录存在
        os.makedirs(self.backup_dir, exist_ok=True)

    def _generate_operation_id(self) -> str:
        """生成操作ID"""
        timestamp = str(time.time())
        return f"op_{hashlib.md5(timestamp.encode()).hexdigest()[:8]}"

    def _create_file_snapshot(self, file_path: str, operation_id: str) -> Optional[str]:
        """创建文件快照"""
        try:
            if not os.path.exists(file_path):
                return None
            
            # 生成快照ID
            snapshot_id = f"snap_{operation_id}_{int(time.time())}"
            snapshot_path = os.path.join(self.backup_dir, f"{snapshot_id}.backup")
            
            # 复制文件
            shutil.copy2(file_path, snapshot_path)
            
            # 存储快照信息到 ChromaDB
            self._store_snapshot_info(snapshot_id, file_path, operation_id)
            
            return snapshot_id
        except Exception as e:
            self._logger.warning(f"创建文件快照失败: {e}")
            return None

    def _store_snapshot_info(self, snapshot_id: str, file_path: str, operation_id: str):
        """存储快照信息"""
        try:
            content = f"File snapshot for {file_path}"
            metadata = {
                "snapshot_id": snapshot_id,
                "file_path": file_path,
                "operation_id": operation_id,
                "timestamp": time.time(),
                "file_size": os.path.getsize(file_path) if os.path.exists(file_path) else 0,
                "file_hash": self._calculate_file_hash(file_path)
            }
            
            self.data_manager.store_data(
                data_type="file_snapshot",
                content=content,
                metadata=metadata
            )
        except Exception as e:
            self._logger.warning(f"存储快照信息失败: {e}")

    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件哈希"""
        try:
            if not os.path.exists(file_path):
                return ""
            
            with open(file_path, 'rb') as f:
                return hashlib.sha256(f.read()).hexdigest()
        except Exception:
            return ""

    def _store_operation_record(self, operation_data: Dict[str, Any]):
        """存储操作记录"""
        try:
            content = f"Agent operation: {operation_data.get('operation_type', 'unknown')}"
            
            self.data_manager.store_data(
                data_type="agent_operation",
                content=content,
                metadata=operation_data
            )
        except Exception as e:
            self._logger.warning(f"存储操作记录失败: {e}")

    def _get_operation_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取操作历史"""
        try:
            results = self.data_manager.query_data(
                query="agent operation",
                data_type="agent_operation",
                n_results=limit
            )

            if results and results.get("metadatas"):
                # 处理 ChromaDB 返回的数据结构
                metadatas = results["metadatas"]
                if isinstance(metadatas, list) and len(metadatas) > 0:
                    # ChromaDB 返回的是列表的列表
                    if isinstance(metadatas[0], list):
                        return metadatas[0] if metadatas[0] else []
                    else:
                        return metadatas
            return []
        except Exception as e:
            print(f"获取操作历史失败: {e}")
            return []


class UndoTool(BaseVersionTool):
    """撤销工具"""

    def get_definition(self) -> ToolDefinition:
        return ToolDefinition(
            name="undo_operation",
            description="撤销 Agent 的操作",
            parameters={
                "type": "object",
                "properties": {
                    "steps": {
                        "type": "integer",
                        "description": "撤销的步数",
                        "default": 1,
                        "minimum": 1,
                        "maximum": 50,
                    },
                    "operation_type": {
                        "type": "string",
                        "enum": ["file_edit", "file_create", "file_delete", "config_change", "all"],
                        "description": "撤销的操作类型",
                        "default": "all",
                    },
                    "target_files": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "指定撤销的文件列表（可选）",
                    },
                    "dry_run": {
                        "type": "boolean",
                        "description": "是否只预览撤销效果",
                        "default": False,
                    },
                    "confirm_required": {
                        "type": "boolean",
                        "description": "是否需要确认",
                        "default": True,
                    },
                },
                "required": [],
            },
        )

    async def execute(self, request: ToolExecutionRequest) -> ToolExecutionResult:
        """执行撤销操作"""
        start_time = time.time()
        params = request.parameters

        try:
            steps = params.get("steps", 1)
            operation_type = params.get("operation_type", "all")
            target_files = params.get("target_files", [])
            dry_run = params.get("dry_run", False)
            confirm_required = params.get("confirm_required", True)

            # 获取操作历史
            history = self._get_operation_history(steps * 2)  # 获取更多历史以便筛选
            
            if not history:
                return self._create_error_result(
                    "NO_HISTORY", "没有找到可撤销的操作历史"
                )

            # 筛选符合条件的操作
            operations_to_undo = self._filter_operations(
                history, steps, operation_type, target_files
            )

            if not operations_to_undo:
                return self._create_error_result(
                    "NO_MATCHING_OPERATIONS", "没有找到符合条件的操作"
                )

            # 执行撤销
            undo_result = self._perform_undo(operations_to_undo, dry_run)

            if not undo_result["success"]:
                return self._create_error_result(
                    "UNDO_FAILED", undo_result["error"]
                )

            # 创建执行元数据
            metadata = ExecutionMetadata(
                execution_time=(time.time() - start_time) * 1000,
                memory_used=len(str(undo_result)) / 1024 / 1024,
                cpu_time=(time.time() - start_time) * 1000,
                io_operations=len(operations_to_undo),
            )

            resources = ResourceUsage(
                memory_mb=len(str(undo_result)) / 1024 / 1024,
                cpu_time_ms=(time.time() - start_time) * 1000,
                io_operations=len(operations_to_undo),
            )

            return self._create_success_result(undo_result, metadata, resources)

        except Exception as e:
            self._logger.exception("撤销操作执行异常")
            return self._create_error_result("EXECUTION_ERROR", f"执行异常: {str(e)}")

    def _filter_operations(
        self, history: List[Dict[str, Any]], steps: int, 
        operation_type: str, target_files: List[str]
    ) -> List[Dict[str, Any]]:
        """筛选要撤销的操作"""
        filtered = []
        
        for op in history:
            # 类型筛选
            if operation_type != "all" and op.get("operation_type") != operation_type:
                continue
            
            # 文件筛选
            if target_files:
                op_file = op.get("file_path", "")
                if not any(target in op_file for target in target_files):
                    continue
            
            # 检查是否已经被撤销
            if op.get("undone", False):
                continue
            
            filtered.append(op)
            
            if len(filtered) >= steps:
                break
        
        return filtered

    def _perform_undo(self, operations: List[Dict[str, Any]], dry_run: bool) -> Dict[str, Any]:
        """执行撤销操作"""
        try:
            undone_operations = []
            
            for op in operations:
                operation_type = op.get("operation_type")
                file_path = op.get("file_path")
                operation_id = op.get("operation_id")
                
                if operation_type == "file_edit":
                    result = self._undo_file_edit(file_path, operation_id, dry_run)
                elif operation_type == "file_create":
                    result = self._undo_file_create(file_path, dry_run)
                elif operation_type == "file_delete":
                    result = self._undo_file_delete(file_path, operation_id, dry_run)
                else:
                    result = {"success": False, "error": f"不支持的操作类型: {operation_type}"}
                
                if result["success"]:
                    undone_operations.append({
                        "operation_id": operation_id,
                        "type": operation_type,
                        "file_path": file_path,
                        "description": op.get("description", ""),
                        "timestamp": op.get("timestamp"),
                        "undo_result": result
                    })
                    
                    # 标记为已撤销（如果不是预览模式）
                    if not dry_run:
                        self._mark_operation_undone(operation_id)
                else:
                    return {"success": False, "error": result["error"]}
            
            return {
                "success": True,
                "undo_summary": {
                    "operations_undone": len(undone_operations),
                    "files_affected": list(set(op["file_path"] for op in undone_operations)),
                    "timestamp": time.time()
                },
                "undone_operations": undone_operations,
                "dry_run": dry_run
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _undo_file_edit(self, file_path: str, operation_id: str, dry_run: bool) -> Dict[str, Any]:
        """撤销文件编辑"""
        try:
            # 查找对应的快照
            snapshot_info = self._find_snapshot(file_path, operation_id)
            if not snapshot_info:
                return {"success": False, "error": f"未找到文件 {file_path} 的快照"}
            
            snapshot_path = os.path.join(self.backup_dir, f"{snapshot_info['snapshot_id']}.backup")
            
            if not os.path.exists(snapshot_path):
                return {"success": False, "error": f"快照文件不存在: {snapshot_path}"}
            
            if not dry_run:
                # 恢复文件
                shutil.copy2(snapshot_path, file_path)
            
            return {
                "success": True,
                "action": "file_restored",
                "snapshot_used": snapshot_info["snapshot_id"]
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _undo_file_create(self, file_path: str, dry_run: bool) -> Dict[str, Any]:
        """撤销文件创建"""
        try:
            if not os.path.exists(file_path):
                return {"success": True, "action": "file_already_deleted"}
            
            if not dry_run:
                os.remove(file_path)
            
            return {"success": True, "action": "file_deleted"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _undo_file_delete(self, file_path: str, operation_id: str, dry_run: bool) -> Dict[str, Any]:
        """撤销文件删除"""
        try:
            # 查找删除前的快照
            snapshot_info = self._find_snapshot(file_path, operation_id)
            if not snapshot_info:
                return {"success": False, "error": f"未找到文件 {file_path} 的删除前快照"}
            
            snapshot_path = os.path.join(self.backup_dir, f"{snapshot_info['snapshot_id']}.backup")
            
            if not os.path.exists(snapshot_path):
                return {"success": False, "error": f"快照文件不存在: {snapshot_path}"}
            
            if not dry_run:
                # 恢复文件
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                shutil.copy2(snapshot_path, file_path)
            
            return {
                "success": True,
                "action": "file_restored",
                "snapshot_used": snapshot_info["snapshot_id"]
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _find_snapshot(self, file_path: str, operation_id: str) -> Optional[Dict[str, Any]]:
        """查找快照信息"""
        try:
            results = self.data_manager.query_data(
                query=f"snapshot for {file_path}",
                data_type="file_snapshot",
                n_results=50
            )

            if results and results.get("metadatas"):
                # 处理 ChromaDB 返回的数据结构
                metadatas = results["metadatas"]
                if isinstance(metadatas, list) and len(metadatas) > 0:
                    if isinstance(metadatas[0], list):
                        metadatas = metadatas[0] if metadatas[0] else []

                for metadata in metadatas:
                    if not isinstance(metadata, dict):
                        continue
                    if (metadata.get("file_path") == file_path and
                        metadata.get("operation_id") == operation_id):
                        return metadata

            return None
        except Exception as e:
            print(f"查找快照失败: {e}")
            return None

    def _mark_operation_undone(self, operation_id: str):
        """标记操作为已撤销"""
        try:
            # 这里应该更新 ChromaDB 中的记录，标记为已撤销
            # 由于 ChromaDB 不支持直接更新，我们添加一个撤销记录
            content = f"Operation {operation_id} has been undone"
            metadata = {
                "operation_id": operation_id,
                "action": "undo_marker",
                "timestamp": time.time()
            }
            
            self.data_manager.store_data(
                data_type="undo_marker",
                content=content,
                metadata=metadata
            )
        except Exception as e:
            self._logger.warning(f"标记操作撤销失败: {e}")

    async def cleanup(self) -> None:
        """清理资源"""
        pass


class RollbackTool(BaseVersionTool):
    """回滚工具"""

    def get_definition(self) -> ToolDefinition:
        return ToolDefinition(
            name="rollback_to_checkpoint",
            description="回滚到指定的检查点或时间点",
            parameters={
                "type": "object",
                "properties": {
                    "target": {
                        "type": "object",
                        "properties": {
                            "checkpoint_id": {"type": "string"},
                            "timestamp": {"type": "string"},
                            "operation_id": {"type": "string"},
                        },
                        "description": "回滚目标（三选一）",
                    },
                    "scope": {
                        "type": "string",
                        "enum": ["project", "files", "config"],
                        "description": "回滚范围",
                        "default": "project",
                    },
                    "include_files": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "包含的文件列表",
                    },
                    "exclude_files": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "排除的文件列表",
                    },
                    "preview_changes": {
                        "type": "boolean",
                        "description": "是否预览变更",
                        "default": True,
                    },
                    "create_backup": {
                        "type": "boolean",
                        "description": "是否在回滚前创建备份",
                        "default": True,
                    },
                },
                "required": ["target"],
            },
        )

    async def execute(self, request: ToolExecutionRequest) -> ToolExecutionResult:
        """执行回滚操作"""
        start_time = time.time()
        params = request.parameters

        try:
            target = params["target"]
            scope = params.get("scope", "project")
            include_files = params.get("include_files", [])
            exclude_files = params.get("exclude_files", [])
            preview_changes = params.get("preview_changes", True)
            create_backup = params.get("create_backup", True)

            # 确定回滚目标
            rollback_target = self._determine_rollback_target(target)
            if not rollback_target["success"]:
                return self._create_error_result(
                    "INVALID_TARGET", rollback_target["error"]
                )

            # 获取回滚点信息
            target_info = rollback_target["target_info"]

            # 分析回滚影响
            impact_analysis = self._analyze_rollback_impact(
                target_info, scope, include_files, exclude_files
            )

            if preview_changes:
                # 只返回预览信息
                result_data = {
                    "rollback_target": target_info,
                    "impact_analysis": impact_analysis,
                    "preview_mode": True,
                    "estimated_changes": impact_analysis["estimated_changes"]
                }
            else:
                # 执行实际回滚
                if create_backup:
                    backup_result = self._create_rollback_backup()
                    if not backup_result["success"]:
                        return self._create_error_result(
                            "BACKUP_FAILED", backup_result["error"]
                        )

                rollback_result = self._perform_rollback(
                    target_info, scope, include_files, exclude_files
                )

                if not rollback_result["success"]:
                    return self._create_error_result(
                        "ROLLBACK_FAILED", rollback_result["error"]
                    )

                result_data = {
                    "rollback_target": target_info,
                    "rollback_summary": rollback_result["summary"],
                    "backup_created": backup_result.get("backup_id") if create_backup else None,
                    "files_affected": rollback_result["files_affected"],
                    "preview_mode": False
                }

            # 创建执行元数据
            metadata = ExecutionMetadata(
                execution_time=(time.time() - start_time) * 1000,
                memory_used=len(str(result_data)) / 1024 / 1024,
                cpu_time=(time.time() - start_time) * 1000,
                io_operations=len(impact_analysis.get("files_to_change", [])),
            )

            resources = ResourceUsage(
                memory_mb=len(str(result_data)) / 1024 / 1024,
                cpu_time_ms=(time.time() - start_time) * 1000,
                io_operations=len(impact_analysis.get("files_to_change", [])),
            )

            return self._create_success_result(result_data, metadata, resources)

        except Exception as e:
            self._logger.exception("回滚操作执行异常")
            return self._create_error_result("EXECUTION_ERROR", f"执行异常: {str(e)}")

    def _determine_rollback_target(self, target: Dict[str, Any]) -> Dict[str, Any]:
        """确定回滚目标"""
        try:
            if "checkpoint_id" in target:
                return self._find_checkpoint(target["checkpoint_id"])
            elif "timestamp" in target:
                return self._find_by_timestamp(target["timestamp"])
            elif "operation_id" in target:
                return self._find_by_operation(target["operation_id"])
            else:
                return {"success": False, "error": "必须指定 checkpoint_id、timestamp 或 operation_id"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _find_checkpoint(self, checkpoint_id: str) -> Dict[str, Any]:
        """查找检查点"""
        try:
            results = self.data_manager.query_data(
                query="checkpoint",
                data_type="checkpoint",
                n_results=50
            )

            if results and results.get("metadatas"):
                # 处理 ChromaDB 返回的数据结构
                metadatas = results["metadatas"]
                if isinstance(metadatas, list) and len(metadatas) > 0:
                    if isinstance(metadatas[0], list):
                        metadatas = metadatas[0] if metadatas[0] else []

                for metadata in metadatas:
                    if not isinstance(metadata, dict):
                        continue
                    if metadata.get("checkpoint_id") == checkpoint_id:
                        # 解析 JSON 字符串
                        try:
                            included_files = json.loads(metadata.get("included_files_str", "[]"))
                        except:
                            included_files = []

                        return {
                            "success": True,
                            "target_info": {
                                "type": "checkpoint",
                                "checkpoint_id": checkpoint_id,
                                "timestamp": metadata.get("timestamp"),
                                "description": metadata.get("description", ""),
                                "included_files": included_files
                            }
                        }

            return {"success": False, "error": f"未找到检查点: {checkpoint_id}"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _find_by_timestamp(self, timestamp_str: str) -> Dict[str, Any]:
        """根据时间戳查找"""
        try:
            # 解析时间戳
            target_timestamp = float(timestamp_str)

            # 查找最接近的操作
            results = self.data_manager.query_data(
                query="agent operation",
                data_type="agent_operation",
                n_results=100
            )

            if results and results.get("metadatas"):
                # 处理 ChromaDB 返回的数据结构
                metadatas = results["metadatas"]
                if isinstance(metadatas, list) and len(metadatas) > 0:
                    if isinstance(metadatas[0], list):
                        metadatas = metadatas[0] if metadatas[0] else []

                closest_op = None
                min_diff = float('inf')

                for metadata in metadatas:
                    if not isinstance(metadata, dict):
                        continue
                    op_timestamp = metadata.get("timestamp", 0)
                    diff = abs(op_timestamp - target_timestamp)
                    if diff < min_diff:
                        min_diff = diff
                        closest_op = metadata

                if closest_op:
                    return {
                        "success": True,
                        "target_info": {
                            "type": "timestamp",
                            "operation_id": closest_op.get("operation_id"),
                            "timestamp": closest_op.get("timestamp"),
                            "description": f"最接近 {timestamp_str} 的操作"
                        }
                    }

            return {"success": False, "error": f"未找到时间戳 {timestamp_str} 附近的操作"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _find_by_operation(self, operation_id: str) -> Dict[str, Any]:
        """根据操作ID查找"""
        try:
            results = self.data_manager.query_data(
                query=f"operation {operation_id}",
                data_type="agent_operation",
                n_results=10
            )

            if results and results.get("metadatas"):
                # 处理 ChromaDB 返回的数据结构
                metadatas = results["metadatas"]
                if isinstance(metadatas, list) and len(metadatas) > 0:
                    if isinstance(metadatas[0], list):
                        metadatas = metadatas[0] if metadatas[0] else []

                for metadata in metadatas:
                    if not isinstance(metadata, dict):
                        continue
                    if metadata.get("operation_id") == operation_id:
                        return {
                            "success": True,
                            "target_info": {
                                "type": "operation",
                                "operation_id": operation_id,
                                "timestamp": metadata.get("timestamp"),
                                "description": metadata.get("description", "")
                            }
                        }

            return {"success": False, "error": f"未找到操作: {operation_id}"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _analyze_rollback_impact(
        self, target_info: Dict[str, Any], scope: str,
        include_files: List[str], exclude_files: List[str]
    ) -> Dict[str, Any]:
        """分析回滚影响"""
        try:
            target_timestamp = target_info.get("timestamp", time.time())

            # 获取目标时间点之后的所有操作
            recent_operations = self._get_operations_since(target_timestamp)

            # 分析受影响的文件
            affected_files = set()
            for op in recent_operations:
                file_path = op.get("file_path")
                if file_path:
                    # 应用文件过滤
                    if include_files and not any(inc in file_path for inc in include_files):
                        continue
                    if exclude_files and any(exc in file_path for exc in exclude_files):
                        continue

                    affected_files.add(file_path)

            return {
                "target_timestamp": target_timestamp,
                "operations_to_rollback": len(recent_operations),
                "files_to_change": list(affected_files),
                "estimated_changes": {
                    "files_affected": len(affected_files),
                    "operations_reversed": len(recent_operations)
                },
                "risk_assessment": self._assess_rollback_risk(recent_operations)
            }
        except Exception as e:
            return {"error": str(e)}

    def _get_operations_since(self, timestamp: float) -> List[Dict[str, Any]]:
        """获取指定时间点之后的操作"""
        try:
            results = self.data_manager.query_data(
                query="agent operation",
                data_type="agent_operation",
                n_results=200
            )

            if results and results.get("metadatas"):
                # 处理 ChromaDB 返回的数据结构
                metadatas = results["metadatas"]
                if isinstance(metadatas, list) and len(metadatas) > 0:
                    if isinstance(metadatas[0], list):
                        metadatas = metadatas[0] if metadatas[0] else []

                return [
                    op for op in metadatas
                    if isinstance(op, dict) and op.get("timestamp", 0) > timestamp
                ]
            return []
        except Exception as e:
            print(f"获取操作历史失败: {e}")
            return []

    def _assess_rollback_risk(self, operations: List[Dict[str, Any]]) -> str:
        """评估回滚风险"""
        if len(operations) > 50:
            return "high"
        elif len(operations) > 10:
            return "medium"
        else:
            return "low"

    def _create_rollback_backup(self) -> Dict[str, Any]:
        """创建回滚前备份"""
        try:
            backup_id = f"rollback_backup_{int(time.time())}"
            # 这里应该创建当前状态的完整备份
            # 简化实现，只记录备份ID

            content = f"Rollback backup created"
            metadata = {
                "backup_id": backup_id,
                "backup_type": "rollback_backup",
                "timestamp": time.time()
            }

            self.data_manager.store_data(
                data_type="rollback_backup",
                content=content,
                metadata=metadata
            )

            return {"success": True, "backup_id": backup_id}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _perform_rollback(
        self, target_info: Dict[str, Any], scope: str,
        include_files: List[str], exclude_files: List[str]
    ) -> Dict[str, Any]:
        """执行实际回滚"""
        try:
            target_timestamp = target_info.get("timestamp", time.time())
            operations_to_rollback = self._get_operations_since(target_timestamp)

            # 按时间倒序排列，从最新的开始撤销
            operations_to_rollback.sort(key=lambda x: x.get("timestamp", 0), reverse=True)

            files_affected = []
            rollback_summary = {
                "operations_rolled_back": 0,
                "files_restored": 0,
                "errors": []
            }

            for op in operations_to_rollback:
                file_path = op.get("file_path")
                if not file_path:
                    continue

                # 应用文件过滤
                if include_files and not any(inc in file_path for inc in include_files):
                    continue
                if exclude_files and any(exc in file_path for exc in exclude_files):
                    continue

                # 执行单个操作的回滚
                result = self._rollback_single_operation(op)
                if result["success"]:
                    rollback_summary["operations_rolled_back"] += 1
                    if file_path not in files_affected:
                        files_affected.append(file_path)
                        rollback_summary["files_restored"] += 1
                else:
                    rollback_summary["errors"].append({
                        "operation_id": op.get("operation_id"),
                        "error": result["error"]
                    })

            return {
                "success": True,
                "summary": rollback_summary,
                "files_affected": files_affected
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _rollback_single_operation(self, operation: Dict[str, Any]) -> Dict[str, Any]:
        """回滚单个操作"""
        try:
            operation_type = operation.get("operation_type")
            file_path = operation.get("file_path")
            operation_id = operation.get("operation_id")

            if operation_type == "file_edit":
                return self._undo_file_edit(file_path, operation_id, False)
            elif operation_type == "file_create":
                return self._undo_file_create(file_path, False)
            elif operation_type == "file_delete":
                return self._undo_file_delete(file_path, operation_id, False)
            else:
                return {"success": True, "action": "skipped_unsupported_operation"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _undo_file_edit(self, file_path: str, operation_id: str, dry_run: bool) -> Dict[str, Any]:
        """撤销文件编辑（复用 UndoTool 的逻辑）"""
        try:
            snapshot_info = self._find_snapshot(file_path, operation_id)
            if not snapshot_info:
                return {"success": False, "error": f"未找到文件 {file_path} 的快照"}

            snapshot_path = os.path.join(self.backup_dir, f"{snapshot_info['snapshot_id']}.backup")

            if not os.path.exists(snapshot_path):
                return {"success": False, "error": f"快照文件不存在: {snapshot_path}"}

            if not dry_run:
                shutil.copy2(snapshot_path, file_path)

            return {"success": True, "action": "file_restored"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _undo_file_create(self, file_path: str, dry_run: bool) -> Dict[str, Any]:
        """撤销文件创建"""
        try:
            if not os.path.exists(file_path):
                return {"success": True, "action": "file_already_deleted"}

            if not dry_run:
                os.remove(file_path)

            return {"success": True, "action": "file_deleted"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _undo_file_delete(self, file_path: str, operation_id: str, dry_run: bool) -> Dict[str, Any]:
        """撤销文件删除"""
        try:
            snapshot_info = self._find_snapshot(file_path, operation_id)
            if not snapshot_info:
                return {"success": False, "error": f"未找到文件 {file_path} 的删除前快照"}

            snapshot_path = os.path.join(self.backup_dir, f"{snapshot_info['snapshot_id']}.backup")

            if not os.path.exists(snapshot_path):
                return {"success": False, "error": f"快照文件不存在: {snapshot_path}"}

            if not dry_run:
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                shutil.copy2(snapshot_path, file_path)

            return {"success": True, "action": "file_restored"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _find_snapshot(self, file_path: str, operation_id: str) -> Optional[Dict[str, Any]]:
        """查找快照信息（复用 UndoTool 的逻辑）"""
        try:
            results = self.data_manager.query_data(
                query=f"snapshot for {file_path}",
                data_type="file_snapshot",
                n_results=50
            )

            if results and results.get("metadatas"):
                for metadata in results["metadatas"]:
                    if (metadata.get("file_path") == file_path and
                        metadata.get("operation_id") == operation_id):
                        return metadata

            return None
        except Exception as e:
            self._logger.warning(f"查找快照失败: {e}")
            return None

    async def cleanup(self) -> None:
        """清理资源"""
        pass


class CheckpointTool(BaseVersionTool):
    """检查点管理工具"""

    def get_definition(self) -> ToolDefinition:
        return ToolDefinition(
            name="manage_checkpoint",
            description="管理版本检查点",
            parameters={
                "type": "object",
                "properties": {
                    "action": {
                        "type": "string",
                        "enum": ["create", "list", "delete", "info"],
                        "description": "操作类型",
                    },
                    "checkpoint_name": {
                        "type": "string",
                        "description": "检查点名称（创建和删除时必需）",
                    },
                    "description": {
                        "type": "string",
                        "description": "检查点描述",
                    },
                    "include_files": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "包含的文件模式列表",
                    },
                    "exclude_files": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "排除的文件模式列表",
                    },
                    "auto_cleanup": {
                        "type": "boolean",
                        "description": "是否自动清理旧检查点",
                        "default": True,
                    },
                    "include_metadata": {
                        "type": "boolean",
                        "description": "是否包含详细元数据",
                        "default": False,
                    },
                },
                "required": ["action"],
            },
        )

    async def execute(self, request: ToolExecutionRequest) -> ToolExecutionResult:
        """执行检查点管理操作"""
        start_time = time.time()
        params = request.parameters

        try:
            action = params["action"]

            if action == "create":
                result = await self._create_checkpoint(params)
            elif action == "list":
                result = await self._list_checkpoints(params)
            elif action == "delete":
                result = await self._delete_checkpoint(params)
            elif action == "info":
                result = await self._get_checkpoint_info(params)
            else:
                return self._create_error_result(
                    "INVALID_ACTION", f"不支持的操作: {action}"
                )

            if not result["success"]:
                return self._create_error_result(
                    "CHECKPOINT_OPERATION_FAILED", result["error"]
                )

            # 创建执行元数据
            metadata = ExecutionMetadata(
                execution_time=(time.time() - start_time) * 1000,
                memory_used=len(str(result)) / 1024 / 1024,
                cpu_time=(time.time() - start_time) * 1000,
                io_operations=1,
            )

            resources = ResourceUsage(
                memory_mb=len(str(result)) / 1024 / 1024,
                cpu_time_ms=(time.time() - start_time) * 1000,
                io_operations=1,
            )

            return self._create_success_result(result["data"], metadata, resources)

        except Exception as e:
            self._logger.exception("检查点管理操作执行异常")
            return self._create_error_result("EXECUTION_ERROR", f"执行异常: {str(e)}")

    async def _create_checkpoint(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """创建检查点"""
        try:
            checkpoint_name = params.get("checkpoint_name")
            if not checkpoint_name:
                return {"success": False, "error": "创建检查点时必须提供 checkpoint_name"}

            description = params.get("description", "")
            include_files = params.get("include_files", [])
            exclude_files = params.get("exclude_files", [])
            auto_cleanup = params.get("auto_cleanup", True)

            # 生成检查点ID
            checkpoint_id = f"checkpoint_{int(time.time())}_{hashlib.md5(checkpoint_name.encode()).hexdigest()[:8]}"

            # 检查是否已存在同名检查点
            existing = await self._find_checkpoint_by_name(checkpoint_name)
            if existing:
                return {"success": False, "error": f"检查点 '{checkpoint_name}' 已存在"}

            # 分析当前项目状态
            project_state = self._analyze_project_state(include_files, exclude_files)

            # 创建文件快照
            snapshots_created = []
            for file_path in project_state["files"]:
                snapshot_id = self._create_file_snapshot(file_path, checkpoint_id)
                if snapshot_id:
                    snapshots_created.append({
                        "file_path": file_path,
                        "snapshot_id": snapshot_id
                    })

            # 存储检查点信息（ChromaDB 只支持基本类型）
            checkpoint_data = {
                "checkpoint_id": checkpoint_id,
                "name": checkpoint_name,
                "description": description,
                "timestamp": time.time(),
                "created_by": "agent",
                "total_files": project_state["total_files"],
                "total_size": project_state["total_size"],
                "included_files_str": json.dumps(include_files),
                "excluded_files_str": json.dumps(exclude_files),
                "snapshots_count": len(snapshots_created)
            }

            content = f"Checkpoint: {checkpoint_name}"
            self.data_manager.store_data(
                data_type="checkpoint",
                content=content,
                metadata=checkpoint_data
            )

            # 自动清理旧检查点
            if auto_cleanup:
                await self._cleanup_old_checkpoints()

            return {
                "success": True,
                "data": {
                    "checkpoint_id": checkpoint_id,
                    "name": checkpoint_name,
                    "description": description,
                    "timestamp": checkpoint_data["timestamp"],
                    "files_included": len(snapshots_created),
                    "project_state": project_state
                }
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _list_checkpoints(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """列出检查点"""
        try:
            include_metadata = params.get("include_metadata", False)

            results = self.data_manager.query_data(
                query="",
                data_type="checkpoint",
                n_results=100
            )

            checkpoints = []
            if results and results.get("metadatas"):
                # 处理 ChromaDB 返回的数据结构
                metadatas = results["metadatas"]
                if isinstance(metadatas, list) and len(metadatas) > 0:
                    if isinstance(metadatas[0], list):
                        metadatas = metadatas[0] if metadatas[0] else []

                for metadata in metadatas:
                    if not isinstance(metadata, dict):
                        continue

                    checkpoint_info = {
                        "checkpoint_id": metadata.get("checkpoint_id"),
                        "name": metadata.get("name"),
                        "description": metadata.get("description", ""),
                        "timestamp": metadata.get("timestamp"),
                        "created_by": metadata.get("created_by", "unknown"),
                        "files_count": metadata.get("snapshots_count", 0)
                    }

                    if include_metadata:
                        checkpoint_info["total_files"] = metadata.get("total_files", 0)
                        checkpoint_info["total_size"] = metadata.get("total_size", 0)
                        # 解析 JSON 字符串
                        try:
                            checkpoint_info["included_files"] = json.loads(metadata.get("included_files_str", "[]"))
                            checkpoint_info["excluded_files"] = json.loads(metadata.get("excluded_files_str", "[]"))
                        except:
                            checkpoint_info["included_files"] = []
                            checkpoint_info["excluded_files"] = []

                    checkpoints.append(checkpoint_info)

            # 按时间排序
            checkpoints.sort(key=lambda x: x.get("timestamp", 0), reverse=True)

            return {
                "success": True,
                "data": {
                    "total_checkpoints": len(checkpoints),
                    "checkpoints": checkpoints
                }
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _delete_checkpoint(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """删除检查点"""
        try:
            checkpoint_name = params.get("checkpoint_name")
            if not checkpoint_name:
                return {"success": False, "error": "删除检查点时必须提供 checkpoint_name"}

            # 查找检查点
            checkpoint_info = await self._find_checkpoint_by_name(checkpoint_name)
            if not checkpoint_info:
                return {"success": False, "error": f"未找到检查点: {checkpoint_name}"}

            # 删除相关的快照文件（简化处理，因为快照信息存储方式已改变）
            deleted_snapshots = 0
            snapshots_count = checkpoint_info.get("snapshots_count", 0)

            # 尝试删除可能的快照文件
            checkpoint_id = checkpoint_info["checkpoint_id"]
            backup_files = [f for f in os.listdir(self.backup_dir) if f.startswith(f"snap_{checkpoint_id}")]

            for backup_file in backup_files:
                backup_path = os.path.join(self.backup_dir, backup_file)
                try:
                    os.remove(backup_path)
                    deleted_snapshots += 1
                except Exception as e:
                    print(f"删除快照文件失败: {e}")

            # 标记检查点为已删除（由于 ChromaDB 不支持直接删除，我们添加删除标记）
            content = f"Checkpoint {checkpoint_name} deleted"
            metadata = {
                "checkpoint_id": checkpoint_info["checkpoint_id"],
                "action": "delete_marker",
                "timestamp": time.time(),
                "original_name": checkpoint_name
            }

            self.data_manager.store_data(
                data_type="checkpoint_deletion",
                content=content,
                metadata=metadata
            )

            return {
                "success": True,
                "data": {
                    "checkpoint_name": checkpoint_name,
                    "checkpoint_id": checkpoint_info["checkpoint_id"],
                    "snapshots_deleted": deleted_snapshots,
                    "deletion_timestamp": time.time()
                }
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _get_checkpoint_info(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取检查点详细信息"""
        try:
            checkpoint_name = params.get("checkpoint_name")
            if not checkpoint_name:
                return {"success": False, "error": "获取检查点信息时必须提供 checkpoint_name"}

            checkpoint_info = await self._find_checkpoint_by_name(checkpoint_name)
            if not checkpoint_info:
                return {"success": False, "error": f"未找到检查点: {checkpoint_name}"}

            # 解析 JSON 字符串
            try:
                included_files = json.loads(checkpoint_info.get("included_files_str", "[]"))
                excluded_files = json.loads(checkpoint_info.get("excluded_files_str", "[]"))
            except:
                included_files = []
                excluded_files = []

            return {
                "success": True,
                "data": {
                    "checkpoint_id": checkpoint_info["checkpoint_id"],
                    "name": checkpoint_info["name"],
                    "description": checkpoint_info.get("description", ""),
                    "timestamp": checkpoint_info.get("timestamp"),
                    "created_by": checkpoint_info.get("created_by", "unknown"),
                    "total_files": checkpoint_info.get("total_files", 0),
                    "total_size": checkpoint_info.get("total_size", 0),
                    "included_files": included_files,
                    "excluded_files": excluded_files,
                    "snapshots_count": checkpoint_info.get("snapshots_count", 0)
                }
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _find_checkpoint_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """根据名称查找检查点"""
        try:
            results = self.data_manager.query_data(
                query="checkpoint",
                data_type="checkpoint",
                n_results=50
            )

            if results and results.get("metadatas"):
                # 处理 ChromaDB 返回的数据结构
                metadatas = results["metadatas"]
                if isinstance(metadatas, list) and len(metadatas) > 0:
                    if isinstance(metadatas[0], list):
                        metadatas = metadatas[0] if metadatas[0] else []

                for metadata in metadatas:
                    if not isinstance(metadata, dict):
                        continue
                    if metadata.get("name") == name:
                        # 检查是否已被删除
                        if not await self._is_checkpoint_deleted(metadata.get("checkpoint_id")):
                            return metadata

            return None
        except Exception as e:
            self._logger.warning(f"查找检查点失败: {e}")
            return None

    async def _is_checkpoint_deleted(self, checkpoint_id: str) -> bool:
        """检查检查点是否已被删除"""
        try:
            results = self.data_manager.query_data(
                query=f"checkpoint {checkpoint_id} deleted",
                data_type="checkpoint_deletion",
                n_results=10
            )

            if results and results.get("metadatas"):
                # 处理 ChromaDB 返回的数据结构
                metadatas = results["metadatas"]
                if isinstance(metadatas, list) and len(metadatas) > 0:
                    if isinstance(metadatas[0], list):
                        metadatas = metadatas[0] if metadatas[0] else []

                for metadata in metadatas:
                    if not isinstance(metadata, dict):
                        continue
                    if metadata.get("checkpoint_id") == checkpoint_id:
                        return True

            return False
        except Exception as e:
            return False

    def _analyze_project_state(self, include_files: List[str], exclude_files: List[str]) -> Dict[str, Any]:
        """分析当前项目状态"""
        try:
            current_dir = os.getcwd()
            all_files = []

            # 遍历当前目录
            for root, dirs, files in os.walk(current_dir):
                # 跳过隐藏目录和常见的忽略目录
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules']]

                for file in files:
                    if file.startswith('.'):
                        continue

                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, current_dir)

                    # 应用包含和排除过滤器
                    if include_files:
                        if not any(pattern in rel_path for pattern in include_files):
                            continue

                    if exclude_files:
                        if any(pattern in rel_path for pattern in exclude_files):
                            continue

                    all_files.append(rel_path)

            # 统计信息
            total_size = 0
            file_types = {}

            for file_path in all_files:
                try:
                    size = os.path.getsize(file_path)
                    total_size += size

                    ext = os.path.splitext(file_path)[1].lower()
                    file_types[ext] = file_types.get(ext, 0) + 1
                except Exception:
                    continue

            return {
                "files": all_files,
                "total_files": len(all_files),
                "total_size": total_size,
                "file_types": file_types,
                "analysis_timestamp": time.time()
            }

        except Exception as e:
            self._logger.warning(f"分析项目状态失败: {e}")
            return {
                "files": [],
                "total_files": 0,
                "total_size": 0,
                "file_types": {},
                "analysis_timestamp": time.time(),
                "error": str(e)
            }

    async def _cleanup_old_checkpoints(self):
        """清理旧检查点"""
        try:
            # 获取所有检查点
            results = self.data_manager.query_data(
                query="checkpoint",
                data_type="checkpoint",
                n_results=200
            )

            if not results or not results.get("metadatas"):
                return

            # 处理 ChromaDB 返回的数据结构
            metadatas = results["metadatas"]
            if isinstance(metadatas, list) and len(metadatas) > 0:
                if isinstance(metadatas[0], list):
                    metadatas = metadatas[0] if metadatas[0] else []

            # 按时间排序，保留最新的检查点
            checkpoints = [m for m in metadatas if isinstance(m, dict)]
            checkpoints.sort(key=lambda x: x.get("timestamp", 0), reverse=True)

            # 删除超过限制的旧检查点
            max_checkpoints = self.config.get("max_checkpoints", 20)
            if len(checkpoints) > max_checkpoints:
                old_checkpoints = checkpoints[max_checkpoints:]

                for checkpoint in old_checkpoints:
                    checkpoint_name = checkpoint.get("name")
                    if checkpoint_name:
                        await self._delete_checkpoint({"checkpoint_name": checkpoint_name})

        except Exception as e:
            print(f"清理旧检查点失败: {e}")

    async def cleanup(self) -> None:
        """清理资源"""
        pass


class VersionManagementTools:
    """版本管理工具集"""

    def __init__(self, config: Optional[ConfigDict] = None):
        self.config = config or {}

    def create_tools(self) -> List[BaseTool]:
        """创建所有版本管理工具"""
        return [
            UndoTool(self.config),
            RollbackTool(self.config),
            CheckpointTool(self.config),
        ]
