#!/usr/bin/env python3
"""
测试Git工具的工作目录使用
"""

import sys
import os
import asyncio

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_git_diff_working_directory():
    """测试git_diff_analysis工具的工作目录使用"""
    print("🔍 测试Git diff工具的工作目录使用...")
    print("-" * 50)
    
    try:
        from mcp_toolkit.tools.git_integration import GitDiffTool
        from mcp_toolkit.tools.base import ToolExecutionRequest, ExecutionContext
        
        # 创建工具实例
        tool = GitDiffTool()
        
        # 测试1: 使用正确的工作目录
        print("📋 测试1: 使用正确的工作目录 (/home/<USER>/workspace/dnkit_demo)")
        context = ExecutionContext(
            request_id="test_git_diff",
            session_id="test_session",
            working_directory="/home/<USER>/workspace/dnkit_demo"
        )
        
        request = ToolExecutionRequest(
            tool_name="git_diff_analysis",
            parameters={
                "target": ".",
                "comparison_base": "HEAD",  # 使用HEAD而不是origin/main
                "analysis_level": "semantic",
                "include_context": True,
                "output_format": "unified"
            },
            execution_context=context
        )
        
        print("⏱️ 开始执行git_diff_analysis...")
        result = await tool.execute(request)
        
        print(f"✅ 执行成功: {result.success}")
        if result.success:
            print("🎉 Git diff工具正确使用了工作目录!")
            # 显示结果的关键信息
            if hasattr(result.content, 'get'):
                changes = result.content.get('changes', [])
                print(f"📊 检测到 {len(changes)} 个文件变更")
            else:
                print(f"📊 结果类型: {type(result.content)}")
        else:
            print(f"❌ 执行失败: {result.error}")
            if result.error and "不是 Git 仓库" in str(result.error.message):
                print("🔍 这可能表明工作目录设置有问题")
        
        # 测试2: 使用错误的工作目录
        print("\n📋 测试2: 使用错误的工作目录 (/tmp)")
        context_wrong = ExecutionContext(
            request_id="test_git_diff_wrong",
            session_id="test_session",
            working_directory="/tmp"
        )
        
        request_wrong = ToolExecutionRequest(
            tool_name="git_diff_analysis",
            parameters={
                "target": ".",
                "comparison_base": "origin/main",
                "analysis_level": "semantic",
                "include_context": True,
                "output_format": "unified"
            },
            execution_context=context_wrong
        )
        
        result_wrong = await tool.execute(request_wrong)
        print(f"✅ 执行结果: {result_wrong.success}")
        if not result_wrong.success:
            print("🎉 工具正确检测到错误的工作目录!")
            print(f"📋 错误信息: {result_wrong.error.message}")
        else:
            print("⚠️ 工具没有检测到错误的工作目录")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_working_directory_inheritance():
    """测试工作目录是否正确传递"""
    print("\n🔍 测试工作目录传递机制...")
    print("-" * 50)
    
    try:
        # 检查当前工作目录
        current_cwd = os.getcwd()
        print(f"📂 当前进程工作目录: {current_cwd}")
        
        # 检查目标目录是否存在且为Git仓库
        target_dir = "/home/<USER>/workspace/dnkit_demo"
        if os.path.exists(target_dir):
            print(f"✅ 目标目录存在: {target_dir}")
            
            # 检查是否为Git仓库
            git_dir = os.path.join(target_dir, ".git")
            if os.path.exists(git_dir):
                print(f"✅ 目标目录是Git仓库")
            else:
                print(f"❌ 目标目录不是Git仓库")
                
            # 检查origin/main是否存在
            import subprocess
            try:
                result = subprocess.run(
                    ["git", "rev-parse", "--verify", "origin/main"],
                    cwd=target_dir,
                    capture_output=True,
                    text=True
                )
                if result.returncode == 0:
                    print("✅ origin/main 分支存在")
                else:
                    print("❌ origin/main 分支不存在")
                    print("尝试获取远程分支...")
                    subprocess.run(["git", "fetch", "origin"], cwd=target_dir, capture_output=True)
            except Exception as git_error:
                print(f"❌ Git命令执行失败: {git_error}")
        else:
            print(f"❌ 目标目录不存在: {target_dir}")
            
    except Exception as e:
        print(f"❌ 工作目录检查失败: {e}")

async def main():
    """主测试函数"""
    print("🧪 Git工具工作目录测试")
    print("=" * 60)
    
    # 测试1: 工作目录传递
    await test_working_directory_inheritance()
    
    # 测试2: Git diff工具
    await test_git_diff_working_directory()
    
    print("\n✅ 所有测试完成!")

if __name__ == "__main__":
    asyncio.run(main())
