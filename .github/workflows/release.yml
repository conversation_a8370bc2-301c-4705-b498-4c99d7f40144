name: Release

on:
  push:
    tags:
      - 'v*'

permissions:
  contents: write

jobs:
  code-quality:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Install uv
      uses: astral-sh/setup-uv@v3
      with:
        version: "latest"

    - name: Set up Python
      run: uv python install 3.12

    - name: Install dependencies
      run: |
        # 安装系统依赖（Logloom需要）
        sudo apt-get update
        sudo apt-get install -y build-essential libyaml-dev pkg-config python3-dev
        # 使用uv安装所有依赖（包括Logloom）
        uv sync --group dev

    - name: Code quality checks
      run: |
        uv run black --check src/
        uv run isort --check-only src/
        uv run flake8 src/
        uv run mypy src/ --ignore-missing-imports
        uv run bandit -r src/

    - name: Validate package structure
      run: |
        uv run python -c "import mcp_toolkit; print('Package imports successfully')"

  release:
    runs-on: ubuntu-latest
    needs: code-quality

    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Install uv
      uses: astral-sh/setup-uv@v3
      with:
        version: "latest"

    - name: Set up Python
      run: uv python install 3.12

    - name: Install dependencies
      run: |
        # 安装系统依赖（Logloom需要）
        sudo apt-get update
        sudo apt-get install -y build-essential libyaml-dev pkg-config python3-dev
        # 使用uv安装所有依赖（包括Logloom）
        uv sync --group dev

    - name: Build package
      run: |
        uv build

    - name: Get tag name
      id: tag
      run: echo "tag=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT

    - name: Generate changelog
      id: changelog
      run: |
        # 简单的变更日志生成
        echo "## 🚀 Release ${{ steps.tag.outputs.tag }}" > CHANGELOG.md
        echo "" >> CHANGELOG.md
        echo "### 新增功能" >> CHANGELOG.md
        echo "- 查看完整的 commit 历史了解详细变更" >> CHANGELOG.md
        echo "" >> CHANGELOG.md
        echo "### 下载" >> CHANGELOG.md
        echo "- Python包: \`pip install mcp-toolkit==${{ steps.tag.outputs.tag }}\`" >> CHANGELOG.md
        echo "- 源码: [mcp-toolkit-${{ steps.tag.outputs.tag }}.tar.gz](https://github.com/${{ github.repository }}/archive/${{ steps.tag.outputs.tag }}.tar.gz)" >> CHANGELOG.md

        # 将内容存储到输出变量
        echo "changelog<<EOF" >> $GITHUB_OUTPUT
        cat CHANGELOG.md >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT

    - name: Create GitHub Release
      uses: softprops/action-gh-release@v2
      with:
        tag_name: ${{ steps.tag.outputs.tag }}
        name: MCP工具集 ${{ steps.tag.outputs.tag }}
        body: ${{ steps.changelog.outputs.changelog }}
        draft: false
        prerelease: ${{ contains(steps.tag.outputs.tag, 'alpha') || contains(steps.tag.outputs.tag, 'beta') || contains(steps.tag.outputs.tag, 'rc') }}
        files: |
          dist/*.whl
          dist/*.tar.gz
        token: ${{ secrets.GITHUB_TOKEN }}
