name: Continuous Integration

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  code-quality:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]

    steps:
    - uses: actions/checkout@v4

    - name: Install uv
      uses: astral-sh/setup-uv@v3
      with:
        version: "latest"

    - name: Set up Python ${{ matrix.python-version }}
      run: uv python install ${{ matrix.python-version }}

    - name: Install dependencies
      run: |
        # 安装系统依赖（Logloom需要）
        sudo apt-get update
        sudo apt-get install -y build-essential cmake libssl-dev zlib1g-dev
        # 克隆并构建Logloom
        git clone https://github.com/ydzat/Logloom.git /tmp/logloom
        cd /tmp/logloom
        uv sync
        uv run python -c "import logloom_py; print('Logloom installed successfully')"
        cd ${{ github.workspace }}
        # 安装项目依赖
        uv sync --group dev

    - name: Lint with flake8
      run: |
        uv run flake8 src/ --count --select=E9,F63,F7,F82 --show-source --statistics
        uv run flake8 src/ --count --exit-zero --max-complexity=10 --max-line-length=88 --statistics

    - name: Format check with black
      run: |
        uv run black --check src/

    - name: Import sort check with isort
      run: |
        uv run isort --check-only src/

    - name: Type check with mypy
      run: |
        uv run mypy src/ --ignore-missing-imports

    - name: Security check with bandit
      run: |
        uv run bandit -r src/ -f json -o bandit-report.json || true
        uv run bandit -r src/

    - name: Validate package structure
      run: |
        uv run python -c "import mcp_toolkit; print('Package imports successfully')"

  mcp-compliance:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Install uv
      uses: astral-sh/setup-uv@v3
      with:
        version: "latest"

    - name: Set up Python
      run: uv python install 3.12

    - name: Install dependencies
      run: |
        # 安装系统依赖（Logloom需要）
        sudo apt-get update
        sudo apt-get install -y build-essential cmake libssl-dev zlib1g-dev
        # 克隆并构建Logloom
        git clone https://github.com/ydzat/Logloom.git /tmp/logloom
        cd /tmp/logloom
        uv sync
        uv run python -c "import logloom_py; print('Logloom installed successfully')"
        cd ${{ github.workspace }}
        # 安装项目依赖
        uv sync --group dev

    - name: Test MCP protocol compliance
      run: |
        uv run python scripts/test_mcp_compliance.py

  security:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Install uv
      uses: astral-sh/setup-uv@v3
      with:
        version: "latest"

    - name: Set up Python
      run: uv python install 3.12

    - name: Install dependencies
      run: |
        # 安装系统依赖（Logloom需要）
        sudo apt-get update
        sudo apt-get install -y build-essential cmake libssl-dev zlib1g-dev
        # 克隆并构建Logloom
        git clone https://github.com/ydzat/Logloom.git /tmp/logloom
        cd /tmp/logloom
        uv sync
        uv run python -c "import logloom_py; print('Logloom installed successfully')"
        cd ${{ github.workspace }}
        # 安装项目依赖
        uv sync --group dev

    - name: Check for known security vulnerabilities
      run: |
        uv run safety check --json --output safety-report.json || true
        uv run safety check

  build:
    runs-on: ubuntu-latest
    needs: [code-quality, mcp-compliance]

    steps:
    - uses: actions/checkout@v4

    - name: Install uv
      uses: astral-sh/setup-uv@v3
      with:
        version: "latest"

    - name: Set up Python
      run: uv python install 3.12

    - name: Install dependencies
      run: |
        # 安装系统依赖（Logloom需要）
        sudo apt-get update
        sudo apt-get install -y build-essential cmake libssl-dev zlib1g-dev
        # 克隆并构建Logloom
        git clone https://github.com/ydzat/Logloom.git /tmp/logloom
        cd /tmp/logloom
        uv sync
        uv run python -c "import logloom_py; print('Logloom installed successfully')"
        cd ${{ github.workspace }}
        # 安装项目依赖
        uv sync --group dev

    - name: Build package
      run: |
        uv build

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: dist
        path: dist/
