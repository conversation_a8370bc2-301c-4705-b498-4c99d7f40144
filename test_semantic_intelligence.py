#!/usr/bin/env python3
"""
语义智能工具测试脚本
测试 CodeCompletionEngine、PatternRecognizer 和 BestPracticeAdvisor 的基本功能
"""

import asyncio
import json
import sys
import os
import warnings
import logging
from pathlib import Path

# 抑制所有警告和日志输出
warnings.filterwarnings("ignore")
logging.getLogger().setLevel(logging.CRITICAL)
logging.getLogger("sentence_transformers").setLevel(logging.CRITICAL)
logging.getLogger("transformers").setLevel(logging.CRITICAL)
logging.getLogger("chromadb").setLevel(logging.CRITICAL)

# 重定向标准错误流以抑制进度条
import sys
from io import StringIO

class SuppressOutput:
    def __enter__(self):
        self._original_stderr = sys.stderr
        sys.stderr = StringIO()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        sys.stderr = self._original_stderr

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from mcp_toolkit.tools.semantic_engine import (
    CodeCompletionEngine,
    PatternRecognizer,
    BestPracticeAdvisor,
    SemanticIntelligenceTools
)
from mcp_toolkit.tools.base import ToolExecutionRequest, ExecutionContext
from mcp_toolkit.storage.unified_manager import UnifiedDataManager


async def test_code_completion():
    """测试代码补全引擎"""
    print("🧠 测试代码补全引擎...")

    # 创建数据管理器
    data_manager = UnifiedDataManager()

    # 创建代码补全引擎
    completion_engine = CodeCompletionEngine()
    completion_engine.data_manager = data_manager

    # 创建测试文件以避免文件不存在错误
    test_file_path = "test_example.py"
    test_code = '''def process_data(data):
    """处理数据的示例函数"""
    result = []
    for item in data:
        if item.is_valid():
            result.append(item.process())
    return result

class DataProcessor:
    def __init__(self):
        self.cache = {}

    def process(self, data):
        return self.transform(data)
'''

    # 写入测试文件
    with open(test_file_path, 'w', encoding='utf-8') as f:
        f.write(test_code)

    # 创建执行上下文
    context = ExecutionContext(
        request_id="test_completion_001",
        working_directory="."
    )

    # 测试请求
    request = ToolExecutionRequest(
        tool_name="get_code_completions",
        parameters={
            "file_path": test_file_path,
            "position": {"line": 10, "column": 4},
            "context": "def process_data(data):\n    result = ",
            "completion_types": ["variables", "functions", "patterns"],
            "max_suggestions": 5
        },
        execution_context=context
    )
    
    try:
        result = await completion_engine.execute(request)
        if result.success:
            print(f"✅ 代码补全测试成功")
            print(f"   状态: {result.success}")
            if result.content:
                completions = result.content.get("completions", [])
                print(f"   生成补全建议: {len(completions)} 个")
                for i, completion in enumerate(completions[:3], 1):
                    print(f"   {i}. {completion.get('text', 'N/A')} ({completion.get('type', 'N/A')})")
            return True
        else:
            print(f"❌ 代码补全测试失败: {result.error}")
            return False
    except Exception as e:
        print(f"❌ 代码补全测试失败: {e}")
        return False
    finally:
        # 清理测试文件
        if os.path.exists(test_file_path):
            os.remove(test_file_path)


async def test_pattern_recognition():
    """测试模式识别器"""
    print("\n🔍 测试模式识别器...")
    
    # 创建数据管理器
    data_manager = UnifiedDataManager()

    # 创建模式识别器
    pattern_recognizer = PatternRecognizer()
    pattern_recognizer.data_manager = data_manager

    # 创建执行上下文
    context = ExecutionContext(
        request_id="test_pattern_001",
        working_directory="."
    )

    # 测试请求
    request = ToolExecutionRequest(
        tool_name="recognize_patterns",
        parameters={
            "target": {
                "type": "code_snippet",
                "content": '''
class DatabaseConnection:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def connect(self):
        pass

class UserFactory:
    @staticmethod
    def create_user(user_type):
        if user_type == "admin":
            return AdminUser()
        elif user_type == "regular":
            return RegularUser()
        return None
'''
            },
            "pattern_types": ["design_patterns", "coding_patterns"],
            "confidence_threshold": 0.7
        },
        execution_context=context
    )
    
    try:
        result = await pattern_recognizer.execute(request)
        if result.success:
            print(f"✅ 模式识别测试成功")
            print(f"   状态: {result.success}")
            if result.content:
                patterns_found = result.content.get("patterns_found", {})
                for pattern_type, patterns in patterns_found.items():
                    if isinstance(patterns, dict) and patterns.get("patterns_by_type"):
                        total = patterns.get("total_patterns", 0)
                        print(f"   {pattern_type}: 发现 {total} 个模式")
                        for ptype, instances in patterns["patterns_by_type"].items():
                            if instances:
                                print(f"     - {ptype}: {len(instances)} 个")
            return True
        else:
            print(f"❌ 模式识别测试失败: {result.error}")
            return False
    except Exception as e:
        print(f"❌ 模式识别测试失败: {e}")
        return False


async def test_best_practice_advisor():
    """测试最佳实践建议器"""
    print("\n📋 测试最佳实践建议器...")
    
    # 创建数据管理器
    data_manager = UnifiedDataManager()

    # 创建最佳实践建议器
    advisor = BestPracticeAdvisor()
    advisor.data_manager = data_manager
    
    # 创建测试文件
    test_file_path = "test_code_sample.py"
    test_code = '''
def very_long_function_with_many_parameters(param1, param2, param3, param4, param5, param6):
    # 这是一个很长的函数，有很多参数
    result = []
    for i in range(100):
        for j in range(100):
            for k in range(10):
                if i > 50:
                    if j > 50:
                        if k > 5:
                            result.append(i * j * k)
                        else:
                            result.append(i + j + k)
                    else:
                        result.append(i - j)
                else:
                    result.append(i)
    return result

class VeryLargeClass:
    def method1(self): pass
    def method2(self): pass
    def method3(self): pass
    def method4(self): pass
    def method5(self): pass
    def method6(self): pass
    def method7(self): pass
    def method8(self): pass
    def method9(self): pass
    def method10(self): pass
    def method11(self): pass
    def method12(self): pass
    def method13(self): pass
    def method14(self): pass
    def method15(self): pass
    def method16(self): pass
    def method17(self): pass
    def method18(self): pass
    def method19(self): pass
    def method20(self): pass
    def method21(self): pass
'''
    
    # 写入测试文件
    with open(test_file_path, 'w', encoding='utf-8') as f:
        f.write(test_code)
    
    try:
        # 创建执行上下文
        context = ExecutionContext(
            request_id="test_advisor_001",
            working_directory="."
        )

        # 测试请求
        request = ToolExecutionRequest(
            tool_name="get_best_practices",
            parameters={
                "target": {
                    "type": "file",
                    "path": test_file_path
                },
                "advice_categories": ["code_quality", "maintainability", "performance"],
                "language": "python",
                "priority_level": "medium"
            },
            execution_context=context
        )
        
        result = await advisor.execute(request)
        if result.success:
            print(f"✅ 最佳实践建议测试成功")
            print(f"   状态: {result.success}")
            if result.content:
                practice_advice = result.content.get("practice_advice", {})
                action_plan = result.content.get("action_plan", [])

                print(f"   分析类别: {len(practice_advice)} 个")
                for category, advice in practice_advice.items():
                    issues = advice.get("issues", [])
                    print(f"     - {category}: {len(issues)} 个问题")

                print(f"   行动计划: {len(action_plan)} 项")
                for i, action in enumerate(action_plan[:3], 1):
                    print(f"     {i}. {action.get('description', 'N/A')} (优先级: {action.get('priority', 'N/A')})")
            return True
        else:
            print(f"❌ 最佳实践建议测试失败: {result.error}")
            return False
    except Exception as e:
        print(f"❌ 最佳实践建议测试失败: {e}")
        return False
    finally:
        # 清理测试文件
        if os.path.exists(test_file_path):
            os.remove(test_file_path)


async def test_semantic_intelligence_tools():
    """测试语义智能工具集"""
    print("\n🛠️ 测试语义智能工具集...")
    
    try:
        # 创建工具集
        tools = SemanticIntelligenceTools()
        tool_instances = tools.create_tools()
        
        print(f"✅ 工具集创建成功")
        print(f"   创建工具数量: {len(tool_instances)}")
        
        for tool in tool_instances:
            definition = tool.get_definition()
            print(f"   - {definition.name}: {definition.description}")
        
        return True
    except Exception as e:
        print(f"❌ 工具集测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始语义智能工具测试\n")
    
    test_results = []
    
    # 运行各项测试
    test_results.append(await test_code_completion())
    test_results.append(await test_pattern_recognition())
    test_results.append(await test_best_practice_advisor())
    test_results.append(await test_semantic_intelligence_tools())
    
    # 汇总结果
    print(f"\n📊 测试结果汇总:")
    print(f"   总测试数: {len(test_results)}")
    print(f"   成功: {sum(test_results)}")
    print(f"   失败: {len(test_results) - sum(test_results)}")
    
    if all(test_results):
        print("🎉 所有测试通过！语义智能工具系统运行正常。")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查相关组件。")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
