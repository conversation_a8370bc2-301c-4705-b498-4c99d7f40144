[project]
name = "mcp-toolkit"
version = "0.1.0"
description = "A modular MCP (Model Context Protocol) toolkit for building extensible tool services"
authors = [
    {name = "Your Name", email = "<EMAIL>"},
]
dependencies = [
    "aiohttp>=3.9.0",
    "pydantic>=2.0.0",
    "pyyaml>=6.0",
    "click>=8.0.0",
    "chromadb>=0.4.0",
    "sentence-transformers>=2.2.0",
    "beautifulsoup4>=4.12.0",
    "psutil>=5.9.0",
    "logloom @ git+https://github.com/ydzat/Logloom.git",
]
requires-python = ">=3.12.11"
readme = "README.md"
license = {text = "MIT"}

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-asyncio>=0.21.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "pre-commit>=3.0.0",
]

[project.scripts]
mcp-toolkit = "mcp_toolkit.main:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/mcp_toolkit"]

[tool.hatch.metadata]
allow-direct-references = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
pythonpath = ["src"]
addopts = [
    "--cov=mcp_toolkit",
    "--cov-report=term-missing",
    "--cov-report=html",
    # "--cov-fail-under=70",  # 暂时移除覆盖率要求，项目开发阶段
]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
exclude = ["tests/", "build/", "dist/"]

[[tool.mypy.overrides]]
module = "logloom_py.*"
ignore_missing_imports = true

# 临时忽略某些文件的类型检查问题，逐步修复
[[tool.mypy.overrides]]
module = "mcp_toolkit.core.logging"
disallow_untyped_defs = false

[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
exclude = '''
/(
    \.git
    | \.venv
    | build
    | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[dependency-groups]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-asyncio>=0.21.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "bandit>=1.8.5",
    "black>=25.1.0",
    "isort>=6.0.1",
    "safety>=3.5.0",
    "types-PyYAML>=6.0.0",
    "pre-commit>=4.2.0",
]
