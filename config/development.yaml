server:
  host: "0.0.0.0"  # 绑定到所有网络接口，允许局域网访问
  port: 8080
  ws_port: 8081  # WebSocket端口
  sse_port: 8082  # SSE端口 (用于n8n MCP连接)
  debug: true

logging:
  level: "DEBUG"
  console: true
  file: "logs/mcp-toolkit.log"

tools:
  categories:
    file_operations:
      enabled: true
      settings:
        max_file_size_bytes: 10485760  # 10MB
        allowed_paths:
          - "./data"
          - "./workspace"
          - "."  # 添加当前工作目录
    terminal:
      enabled: true
    network:
      enabled: true
    search:
      enabled: true

services:
  registry:
    auto_discovery: true
    health_check_interval: 30

security:
  allowed_origins:
    - "http://localhost:*"
    - "http://127.0.0.1:*"
    - "*"  # 允许所有来源（开发环境）
  rate_limiting:
    enabled: true
    requests_per_minute: 60
