# MCP 工具集增强配置
# 基于 ChromaDB 统一存储的增强工具配置

# 工具启用配置
tools:
  categories:
    # 基础工具
    file_operations:
      enabled: true
    terminal:
      enabled: true
    network:
      enabled: true
    search:
      enabled: true

    # 第一阶段增强工具
    enhanced_file_operations:
      enabled: true
    enhanced_network:
      enabled: true
    enhanced_system:
      enabled: true

    # 第二阶段上下文引擎工具
    context_tools:
      enabled: true

    # Git 集成工具（第一阶段增强）
    git_integration:
      enabled: true
      settings:
        git_timeout: 30
        max_diff_size: 1048576  # 1MB
        create_backup: true
        verify_context: true

    # 版本管理工具（第一阶段增强）
    version_management:
      enabled: true
      settings:
        backup_directory: "./mcp_backups"
        max_history_size: 1000
        auto_cleanup_days: 7
        max_checkpoints: 20

    # Agent 自动化工具（第一阶段增强）
    agent_automation:
      enabled: true
      settings:
        max_suggestions: 10
        context_window: 5000

    # 智能分析工具（第二阶段）
    intelligent_analysis:
      enabled: true
      settings:
        analysis_depth: "medium"
        history_window_days: 30

    # Agent 行为工具（第二阶段）
    agent_behavior:
      enabled: true
      settings:
        max_state_history: 1000
        learning_enabled: true

    # 上下文引擎工具（第二阶段）
    context_engine:
      enabled: true
      settings:
        supported_languages: ["python", "javascript", "typescript", "java", "cpp", "go"]
        analysis_depth: "deep"

    # 语义智能工具（第三阶段 - 语义理解和智能推荐）
    semantic_intelligence:
      enabled: true
      settings:
        # 代码补全引擎配置
        code_completion:
          max_suggestions: 10
          context_window: 1000
          semantic_threshold: 0.7
          cache_enabled: true
          cache_ttl: 3600  # 1小时

        # 模式识别器配置
        pattern_recognition:
          confidence_threshold: 0.7
          supported_patterns:
            - design_patterns
            - coding_patterns
            - anti_patterns
            - performance_patterns
            - security_patterns
          analysis_depth: "deep"

        # 最佳实践建议器配置
        best_practices:
          priority_levels: ["low", "medium", "high", "critical"]
          advice_categories:
            - code_quality
            - performance
            - security
            - maintainability
            - testing
          language_specific_rules: true

        # Agent 协作配置
        agent_collaboration:
          enabled: true
          clarification_threshold: 0.6
          validation_threshold: 0.8
          max_dialog_rounds: 3

        # 学习适应配置
        learning:
          user_behavior_tracking: true
          project_specific_learning: true
          feedback_learning: true
          adaptation_rate: 0.1

    # 第三阶段智能化增强工具
    task_management:
      enabled: true
    memory_management:
      enabled: true
    visualization:
      enabled: true
    collaboration:
      enabled: true

# ChromaDB 统一存储配置
chromadb:
  persist_directory: "./mcp_unified_db"
  collection_name: "mcp_unified_storage"
  embedding_model: "sentence-transformers/all-MiniLM-L6-v2"
  anonymized_telemetry: false

# 增强文件操作工具配置
enhanced_file_operations:
  max_file_size_mb: 10
  auto_store_to_chromadb: true
  supported_encodings:
    - utf-8
    - gbk
    - ascii
  language_detection: true
  extract_metadata: true

# 增强网络工具配置
enhanced_network:
  timeout: 30
  max_content_size_mb: 5
  auto_store_to_chromadb: true
  user_agent: "MCP-Toolkit/1.0"
  extract_text: true
  include_metadata: true

# 增强系统工具配置
enhanced_system:
  auto_store_to_chromadb: true
  default_process_limit: 20
  include_system_details: true

# 第二阶段：上下文引擎工具配置
context_tools:
  chunk_size: 1000
  chunk_overlap: 100
  max_file_size_mb: 10
  max_results: 20
  context_engine:
    enable_code_analysis: true
    enable_dependency_tracking: true
    enable_semantic_search: true

# 第三阶段：智能化增强工具配置
task_management:
  auto_store_to_chromadb: true
  max_tasks_per_query: 50
  default_importance_threshold: 0.5
  enable_auto_cleanup: true
  memory_retention_days: 90

memory_management:
  auto_store_to_chromadb: true
  max_memories_per_query: 50
  default_importance_threshold: 0.5
  enable_auto_cleanup: true
  memory_retention_days: 90

visualization:
  default_theme: "default"
  max_nodes: 1000
  enable_auto_generation: true

collaboration:
  max_parallel_tasks: 5
  enable_caching: true
  default_timeout: 300
  enable_rollback: false

# 安全配置
security:
  # 基于智能路径验证的安全设置
  smart_path_validation: true
  allowed_domains: []  # 空表示允许所有域名，可以添加特定域名限制
  max_concurrent_operations: 5

# 性能配置
performance:
  max_memory_usage_mb: 500
  query_timeout_seconds: 30
  batch_size: 16
  cache_size: 1000

# 日志配置
logging:
  level: INFO
  log_chromadb_operations: true
  log_performance_metrics: true
