#!/usr/bin/env python3
"""
测试ChromaDB初始化问题
"""

import sys
import os
import time

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_chromadb_init():
    """测试ChromaDB初始化"""
    print("🔍 测试ChromaDB初始化...")
    print("-" * 50)
    
    try:
        print("步骤1: 导入UnifiedDataManager...")
        from mcp_toolkit.storage.unified_manager import UnifiedDataManager
        
        print("步骤2: 创建UnifiedDataManager实例...")
        start_time = time.time()
        
        # 使用不同的目录避免冲突
        test_dir = "./test_chromadb_data"
        data_manager = UnifiedDataManager(persist_directory=test_dir)
        
        init_time = time.time() - start_time
        print(f"✅ UnifiedDataManager初始化成功，耗时: {init_time:.2f}秒")
        
        print("步骤3: 测试基本操作...")
        # 测试存储
        test_id = data_manager.store_data(
            data_type="test",
            content="测试内容",
            metadata={"test": True, "timestamp": time.time()}
        )
        print(f"✅ 数据存储成功，ID: {test_id}")
        
        # 测试查询
        results = data_manager.search_data(
            query="测试",
            data_types=["test"],
            limit=1
        )
        print(f"✅ 数据查询成功，结果数量: {len(results)}")
        
        # 测试删除
        data_manager.collection.delete(ids=[test_id])
        print("✅ 数据删除成功")
        
        print("🎉 ChromaDB初始化和基本操作测试通过！")
        
        # 清理测试目录
        import shutil
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
            print("🗑️ 测试目录已清理")
        
    except Exception as e:
        print(f"❌ ChromaDB测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_chromadb_reset():
    """测试ChromaDB重置功能"""
    print("\n🔄 测试ChromaDB重置功能...")
    print("-" * 50)
    
    try:
        import shutil
        
        # 清理可能存在的ChromaDB数据
        dirs_to_clean = [
            "./mcp_unified_db",
            "./chromadb_data", 
            "./test_chromadb_data",
            os.path.expanduser("~/.cache/chromadb")
        ]
        
        for dir_path in dirs_to_clean:
            if os.path.exists(dir_path):
                print(f"清理目录: {dir_path}")
                shutil.rmtree(dir_path)
        
        print("✅ ChromaDB数据清理完成")
        
        # 重新测试初始化
        test_chromadb_init()
        
    except Exception as e:
        print(f"❌ ChromaDB重置测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_working_directory_warmup():
    """测试工作目录设置的ChromaDB预热功能"""
    print("\n🔥 测试工作目录ChromaDB预热功能...")
    print("-" * 50)
    
    try:
        from mcp_toolkit.tools.terminal import SetWorkingDirectoryTool
        from mcp_toolkit.tools.base import ToolExecutionRequest, ExecutionContext
        
        # 创建工具实例
        tool = SetWorkingDirectoryTool()
        
        # 创建执行上下文
        context = ExecutionContext(
            request_id="test_warmup",
            session_id="test_session",
            working_directory="/home/<USER>/workspace/dnkit"
        )
        
        # 创建测试请求
        request = ToolExecutionRequest(
            tool_name="set_working_directory",
            parameters={
                "path": "/home/<USER>/workspace/dnkit_demo",
                "create_if_missing": True
            },
            execution_context=context
        )
        
        print("执行工作目录设置（包含ChromaDB预热）...")
        start_time = time.time()
        
        import asyncio
        result = asyncio.run(tool.execute(request))
        
        total_time = time.time() - start_time
        print(f"⏱️ 总执行时间: {total_time:.2f}秒")
        print(f"✅ 执行成功: {result.success}")
        
        if result.success:
            content = result.content
            print(f"📂 新工作目录: {content.get('new_directory')}")
            
            chromadb_status = content.get('chromadb_warmup', {})
            print(f"🔥 ChromaDB预热状态: {chromadb_status.get('status')}")
            print(f"⏱️ 预热时间: {chromadb_status.get('warmup_time_seconds')}秒")
            print(f"💬 预热消息: {chromadb_status.get('message')}")
            
            # 显示调试步骤
            debug_steps = chromadb_status.get('debug_steps', [])
            if debug_steps:
                print("🔍 调试步骤:")
                for i, step in enumerate(debug_steps, 1):
                    print(f"  {i}. {step}")
            
            if chromadb_status.get('status') == 'failed':
                print(f"❌ 预热错误: {chromadb_status.get('error')}")
        else:
            print(f"❌ 执行失败: {result.error}")
            
    except Exception as e:
        print(f"❌ 工作目录预热测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 ChromaDB初始化问题诊断")
    print("=" * 60)
    
    # 测试1: 基础ChromaDB初始化
    test_chromadb_init()
    
    # 测试2: ChromaDB重置功能
    test_chromadb_reset()
    
    # 测试3: 工作目录预热功能
    test_working_directory_warmup()
    
    print("\n✅ 所有测试完成！")
