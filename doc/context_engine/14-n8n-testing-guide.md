# n8n 完整测试指南

## 🎯 测试目标

本指南将帮助您在 n8n 中完整测试 MCP 工具集的所有功能，验证自动化编程服务的完整性和可靠性。

## 🚀 环境准备

### 1. 启动 MCP 服务器

```bash
# 进入项目目录
cd /path/to/dnkit

# 使用 uv 启动服务器
uv run python -m mcp_toolkit.server

# 或者使用配置文件启动
uv run python -m mcp_toolkit.server --config config/enhanced_tools_config.yaml
```

### 2. 配置 n8n MCP 连接

在 n8n 中添加 MCP 连接：

```json
{
  "name": "DNKit MCP Server",
  "type": "mcp",
  "connection": {
    "host": "localhost",
    "port": 8000,
    "protocol": "http"
  }
}
```

### 3. 验证连接

创建一个简单的测试工作流验证连接：

```json
{
  "nodes": [
    {
      "name": "Test Connection",
      "type": "mcp",
      "parameters": {
        "tool": "list_tools",
        "parameters": {}
      }
    }
  ]
}
```

## 📋 测试计划

### 阶段1：基础功能测试 (30分钟)

#### 1.1 文件操作测试

**测试工作流：文件读写操作**

```json
{
  "name": "文件操作测试",
  "nodes": [
    {
      "name": "创建测试文件",
      "type": "mcp",
      "parameters": {
        "tool": "write_file",
        "parameters": {
          "path": "test_file.py",
          "content": "def hello_world():\n    print('Hello, World!')\n    return 'success'"
        }
      }
    },
    {
      "name": "读取文件内容",
      "type": "mcp", 
      "parameters": {
        "tool": "read_file",
        "parameters": {
          "path": "test_file.py"
        }
      }
    },
    {
      "name": "分析文件结构",
      "type": "mcp",
      "parameters": {
        "tool": "analyze_code_structure", 
        "parameters": {
          "file_path": "test_file.py",
          "analysis_type": "functions"
        }
      }
    }
  ]
}
```

**预期结果：**
- 文件创建成功
- 文件内容正确读取
- 代码结构分析识别出 `hello_world` 函数

#### 1.2 Git 操作测试

**测试工作流：Git 集成功能**

```json
{
  "name": "Git操作测试",
  "nodes": [
    {
      "name": "检查Git状态",
      "type": "mcp",
      "parameters": {
        "tool": "git_status",
        "parameters": {
          "repository_path": "."
        }
      }
    },
    {
      "name": "查看文件差异",
      "type": "mcp",
      "parameters": {
        "tool": "git_diff",
        "parameters": {
          "file_path": "test_file.py",
          "staged": false
        }
      }
    },
    {
      "name": "创建检查点",
      "type": "mcp",
      "parameters": {
        "tool": "create_checkpoint",
        "parameters": {
          "name": "测试检查点",
          "description": "n8n测试创建的检查点"
        }
      }
    }
  ]
}
```

### 阶段2：智能分析测试 (45分钟)

#### 2.1 代码质量分析

**测试工作流：代码质量检查**

```json
{
  "name": "代码质量分析",
  "nodes": [
    {
      "name": "创建复杂代码文件",
      "type": "mcp",
      "parameters": {
        "tool": "write_file",
        "parameters": {
          "path": "complex_code.py",
          "content": "def complex_function(a, b, c, d, e, f):\n    if a > 0:\n        if b > 0:\n            if c > 0:\n                if d > 0:\n                    if e > 0:\n                        return f\n    return 0\n\nclass LargeClass:\n    def method1(self): pass\n    def method2(self): pass\n    def method3(self): pass\n    def method4(self): pass\n    def method5(self): pass"
        }
      }
    },
    {
      "name": "分析代码质量",
      "type": "mcp",
      "parameters": {
        "tool": "analyze_code_quality",
        "parameters": {
          "file_path": "complex_code.py",
          "metrics": ["complexity", "maintainability", "readability"]
        }
      }
    },
    {
      "name": "获取最佳实践建议",
      "type": "mcp",
      "parameters": {
        "tool": "get_best_practices",
        "parameters": {
          "target": {
            "type": "file",
            "path": "complex_code.py"
          },
          "advice_categories": ["code_quality", "maintainability"],
          "priority_level": "medium"
        }
      }
    }
  ]
}
```

**预期结果：**
- 检测到高复杂度函数
- 识别出参数过多的问题
- 提供重构建议

#### 2.2 模式识别测试

**测试工作流：设计模式识别**

```json
{
  "name": "模式识别测试",
  "nodes": [
    {
      "name": "创建单例模式代码",
      "type": "mcp",
      "parameters": {
        "tool": "write_file",
        "parameters": {
          "path": "singleton_example.py",
          "content": "class DatabaseConnection:\n    _instance = None\n    \n    def __new__(cls):\n        if cls._instance is None:\n            cls._instance = super().__new__(cls)\n        return cls._instance\n    \n    def connect(self):\n        pass"
        }
      }
    },
    {
      "name": "识别设计模式",
      "type": "mcp",
      "parameters": {
        "tool": "recognize_patterns",
        "parameters": {
          "target": {
            "type": "file",
            "path": "singleton_example.py"
          },
          "pattern_types": ["design_patterns"],
          "confidence_threshold": 0.7
        }
      }
    }
  ]
}
```

**预期结果：**
- 正确识别单例模式
- 置信度 > 0.8
- 提供模式使用建议

### 阶段3：语义理解测试 (30分钟)

#### 3.1 代码补全测试

**测试工作流：智能代码补全**

```json
{
  "name": "代码补全测试",
  "nodes": [
    {
      "name": "创建上下文代码",
      "type": "mcp",
      "parameters": {
        "tool": "write_file",
        "parameters": {
          "path": "context_example.py",
          "content": "class DataProcessor:\n    def __init__(self):\n        self.data = []\n        self.cache = {}\n    \n    def process_data(self, input_data):\n        result = "
        }
      }
    },
    {
      "name": "获取代码补全",
      "type": "mcp",
      "parameters": {
        "tool": "get_code_completions",
        "parameters": {
          "file_path": "context_example.py",
          "position": {"line": 7, "column": 18},
          "context": "def process_data(self, input_data):\n        result = ",
          "completion_types": ["variables", "functions", "patterns"],
          "max_suggestions": 5
        }
      }
    }
  ]
}
```

#### 3.2 语义理解测试

**测试工作流：深度语义分析**

```json
{
  "name": "语义理解测试",
  "nodes": [
    {
      "name": "创建业务逻辑代码",
      "type": "mcp",
      "parameters": {
        "tool": "write_file",
        "parameters": {
          "path": "business_logic.py",
          "content": "class UserManager:\n    def create_user(self, user_data):\n        # 创建新用户\n        pass\n    \n    def update_user(self, user_id, data):\n        # 更新用户信息\n        pass\n    \n    def delete_user(self, user_id):\n        # 删除用户\n        pass"
        }
      }
    },
    {
      "name": "理解语义结构",
      "type": "mcp",
      "parameters": {
        "tool": "understand_semantics",
        "parameters": {
          "target": {
            "type": "file",
            "path": "business_logic.py"
          },
          "understanding_types": ["business_logic", "code_intent"],
          "analysis_depth": "deep"
        }
      }
    }
  ]
}
```

### 阶段4：自动化工作流测试 (45分钟)

#### 4.1 任务分解测试

**测试工作流：复杂任务自动分解**

```json
{
  "name": "任务分解测试",
  "nodes": [
    {
      "name": "分解复杂任务",
      "type": "mcp",
      "parameters": {
        "tool": "decompose_task",
        "parameters": {
          "task_description": "创建一个用户管理系统，包括用户注册、登录、权限管理和数据持久化功能",
          "complexity_level": "high",
          "include_dependencies": true
        }
      }
    },
    {
      "name": "获取执行指导",
      "type": "mcp",
      "parameters": {
        "tool": "get_execution_guidance",
        "parameters": {
          "task_type": "web_development",
          "current_context": "Python Flask项目",
          "user_experience": "intermediate"
        }
      }
    }
  ]
}
```

#### 4.2 Agent 行为测试

**测试工作流：Agent 状态机控制**

```json
{
  "name": "Agent行为测试",
  "nodes": [
    {
      "name": "初始化状态机",
      "type": "mcp",
      "parameters": {
        "tool": "control_state_machine",
        "parameters": {
          "session_id": "test_session_001",
          "action": "initialize",
          "initial_state": "idle"
        }
      }
    },
    {
      "name": "开始任务执行",
      "type": "mcp",
      "parameters": {
        "tool": "control_state_machine",
        "parameters": {
          "session_id": "test_session_001",
          "action": "transition",
          "trigger": "start_task",
          "context": {
            "task_type": "code_analysis",
            "target_files": ["test_file.py"]
          }
        }
      }
    },
    {
      "name": "验证行为",
      "type": "mcp",
      "parameters": {
        "tool": "validate_behavior",
        "parameters": {
          "session_id": "test_session_001",
          "expected_state": "analyzing",
          "validation_rules": ["state_consistency", "transition_validity"]
        }
      }
    }
  ]
}
```

## 📊 测试结果验证

### 成功标准

每个测试阶段都应该满足以下标准：

#### 阶段1：基础功能
- ✅ 文件操作成功率 > 95%
- ✅ Git 操作响应时间 < 2秒
- ✅ 检查点创建成功

#### 阶段2：智能分析  
- ✅ 代码质量问题检测准确率 > 90%
- ✅ 设计模式识别置信度 > 0.8
- ✅ 最佳实践建议相关性 > 85%

#### 阶段3：语义理解
- ✅ 代码补全建议数量 >= 3
- ✅ 语义理解覆盖率 > 80%
- ✅ 业务逻辑识别准确率 > 85%

#### 阶段4：自动化工作流
- ✅ 任务分解完整性 > 90%
- ✅ Agent 状态转换正确性 100%
- ✅ 行为验证通过率 > 95%

### 性能指标

- **响应时间**: 平均 < 1秒，最大 < 5秒
- **内存使用**: < 512MB
- **并发处理**: 支持 10+ 并发请求
- **错误率**: < 1%

## 🔧 故障排除

### 常见问题

1. **连接失败**
   - 检查 MCP 服务器是否正常运行
   - 验证端口配置是否正确
   - 确认防火墙设置

2. **工具执行失败**
   - 检查参数格式是否正确
   - 验证文件路径和权限
   - 查看服务器日志

3. **性能问题**
   - 检查系统资源使用情况
   - 优化 ChromaDB 配置
   - 调整并发限制

### 调试技巧

1. **启用详细日志**
   ```bash
   export LOG_LEVEL=DEBUG
   uv run python -m mcp_toolkit.server
   ```

2. **使用测试模式**
   ```bash
   uv run python test_semantic_intelligence.py
   ```

3. **监控资源使用**
   ```bash
   htop  # 监控 CPU 和内存
   ```

## 📈 测试报告模板

完成测试后，请填写以下报告：

```markdown
# n8n 集成测试报告

## 测试环境
- 操作系统: 
- Python 版本:
- n8n 版本:
- 测试时间:

## 测试结果
- 阶段1 (基础功能): ✅/❌
- 阶段2 (智能分析): ✅/❌  
- 阶段3 (语义理解): ✅/❌
- 阶段4 (自动化工作流): ✅/❌

## 性能指标
- 平均响应时间: 
- 最大内存使用:
- 错误率:

## 发现的问题
1. 
2. 
3. 

## 改进建议
1.
2. 
3.
```

通过这个完整的测试指南，您可以全面验证 MCP 工具集在 n8n 中的功能完整性和性能表现。
