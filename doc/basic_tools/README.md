# MCP Toolkit 基础工具设计文档

本目录包含 MCP Toolkit 基础工具的完整设计文档，旨在构建一个功能完整、可扩展的 MCP 服务器工具集。

## 📋 目录结构

```
doc/basic_tools/
├── README.md                    # 本文件 - 总览
├── 01-architecture.md           # 整体架构设计
├── 02-file-system-tools.md     # 文件系统工具
├── 03-network-tools.md         # 网络工具
├── 04-system-tools.md          # 系统工具
├── 05-database-tools.md        # 数据库工具
├── 06-context-engine.md        # 上下文引擎
├── 07-task-management.md       # 任务管理工具
├── 08-visualization-tools.md   # 可视化工具
├── 09-memory-tools.md          # 记忆和存储工具
├── 10-integration-patterns.md  # 工具集成模式
└── 11-security-model.md        # 安全模型设计
```

## 🎯 设计目标

### 核心目标
1. **功能实用性** - 基于当前实现，提供实用的工具能力
2. **简单安全** - 实现基础的安全验证，避免过度复杂
3. **渐进扩展** - 支持逐步添加新工具和功能
4. **性能适中** - 适合个人和小团队使用的性能要求
5. **易于使用** - 提供简洁的配置和使用体验

### 技术目标
- **模块化设计** - 基于现有架构，逐步扩展工具类别
- **异步支持** - 基础的异步操作支持
- **配置驱动** - 简单的YAML配置文件
- **基础监控** - 提供必要的日志和基础指标
- **错误处理** - 简单有效的错误处理机制

## 🏗️ 工具分类

### 1. 文件系统工具 (File System Tools)
- 文件读写操作
- 目录管理
- 文件搜索和过滤
- 内容处理和转换
- 权限和安全控制

### 2. 网络工具 (Network Tools)
- HTTP/HTTPS 请求
- 网页内容获取和解析
- API 调用和集成
- 网络诊断工具
- 代理和认证支持

### 3. 系统工具 (System Tools)
- 进程管理
- 命令执行
- 系统信息获取
- 资源监控
- 环境变量管理

### 4. 统一数据存储工具 (Unified Data Storage)
- ChromaDB 统一存储架构
- 向量嵌入和语义搜索
- 元数据管理和过滤
- 统一数据操作接口
- 数据类型区分和组织

### 5. 上下文引擎 (Context Engine)
- 基于 ChromaDB 的代码库索引
- 语义相似性检索和智能推荐
- 统一数据查询和聚合
- 多语言代码分析支持
- 与统一存储的深度集成

### 6. 任务管理工具 (Task Management)
- 基于 ChromaDB 的任务存储
- 任务状态管理和跟踪
- 语义搜索任务和智能推荐
- 简化的工作流支持
- 任务关联和依赖管理

### 7. 可视化工具 (Visualization Tools)
- 图表生成 (Mermaid, PlantUML)
- 数据可视化
- 报告生成
- 交互式界面
- 导出功能

### 8. 记忆和存储工具 (Memory & Storage)
- 与上下文引擎共享 ChromaDB 存储
- 统一的记忆数据管理
- 对话历史和知识积累
- 智能记忆检索和关联
- 记忆重要性评估和管理

## 🔄 工具交互模式

### 链式调用模式
工具之间可以形成处理链，前一个工具的输出作为后一个工具的输入。

### 并行执行模式
支持多个工具同时执行，提高处理效率。

### 事件驱动模式
工具可以订阅和发布事件，实现松耦合的协作。

### 管道模式
支持数据流在多个工具之间传递和转换。

## 📊 性能和扩展性

### 性能优化
- 异步 I/O 操作
- 连接池和资源复用
- 缓存机制
- 批量处理支持

### 扩展性设计
- 插件化架构
- 动态工具加载
- 配置热更新
- 水平扩展支持

## 🔒 安全考虑

### 访问控制
- 基于角色的权限管理
- 资源访问限制
- 操作审计日志
- 安全策略配置

### 数据保护
- 敏感数据加密
- 安全传输协议
- 数据脱敏处理
- 隐私保护机制

## 📈 监控和运维

### 监控指标
- 工具执行性能
- 资源使用情况
- 错误率和成功率
- 用户行为分析

### 运维支持
- 健康检查接口
- 配置管理
- 日志聚合
- 故障恢复机制

## 🚀 实施路线图与进度

### ✅ 第一阶段：基础工具完善（基于现有实现）**已完成**
- ✅ **扩展现有文件系统工具功能**
  - 增强文件读取工具（`enhanced_read_file`）- 支持 ChromaDB 存储、语言检测、元数据提取
  - 文件语义搜索工具（`search_files`）- 基于 ChromaDB 的智能搜索
- ✅ **添加基础网络工具（HTTP请求、网页获取）**
  - 增强网页获取工具（`enhanced_fetch_web`）- 支持 ChromaDB 存储、文本提取、元数据解析
  - 网页内容搜索工具（`search_web_content`）- 基于 ChromaDB 的网页内容语义搜索
- ✅ **实现简单系统工具（进程管理、命令执行）**
  - 系统信息获取工具（`get_system_info`）- 支持 ChromaDB 存储
  - 进程管理工具（`manage_processes`）- 列出、搜索、查看进程详情

**完成时间**：2025-07-09
**工具数量**：18个（基础工具12个 + 增强工具6个）
**核心特性**：ChromaDB 统一数据存储、语义搜索、MCP 协议兼容

### ✅ 第二阶段：上下文引擎开发 **已完成**
- ✅ **ChromaDB 统一数据存储实现** - `UnifiedDataManager` 已实现
- ✅ **语义搜索功能** - 基于 sentence-transformers 的语义搜索已集成
- ✅ **上下文引擎核心功能** - 代码分析、智能查询、语义搜索已完成
- ✅ **多语言代码分析** - 支持 Python、JavaScript、TypeScript 等语言
- ✅ **智能查询处理器** - 自然语言查询理解和意图解析
- ✅ **上下文工具集成** - 4个核心上下文工具已实现
- ✅ **相似代码搜索** - 基于语义相似度的代码匹配
- ✅ **项目概览统计** - 代码库整体分析和统计

**完成时间**：2025-07-09
**新增工具**：4个上下文引擎工具
**测试覆盖**：56个单元测试全部通过，n8n 集成测试验证通过

### ✅ 第三阶段：智能化增强 **已完成**
- ✅ **任务管理系统 ChromaDB 集成** - 基于 ChromaDB 的任务存储和语义搜索
  - 通用任务管理工具（`manage_tasks`）- 创建、更新、删除、查询任务
  - 专门搜索工具（`search_recent_tasks`）- 搜索最近创建的任务
  - 时间范围搜索工具（`search_tasks_by_time`）- 按时间范围搜索任务
  - 语义搜索工具（`search_tasks_semantic`）- 基于内容相似性搜索任务
- ✅ **记忆系统与 ChromaDB 统一集成** - 对话历史和知识积累
  - 记忆管理工具（`manage_memory`）- 存储、检索、搜索知识和记忆
  - 支持多种记忆类型（知识、对话、经验、技能）
  - 智能记忆检索和相关性评分
- ✅ **基础可视化工具** - Mermaid 图表生成、数据可视化
  - 通用图表生成工具（`generate_diagram`）- Mermaid 流程图、时序图等
  - 数据图表工具（`create_data_chart`）- Chart.js 格式的数据可视化
  - 复杂子图工具（`generate_subgraph_diagram`）- 支持嵌套模块的复杂图表
  - 状态机图表工具（`generate_state_machine`）- 专门的自动机和状态转换图
- ✅ **工具间协作和数据流优化** - 链式调用、并行执行
  - 工具链执行器（`execute_tool_chain`）- 支持工具链式调用和并行执行

**完成时间**：2025-07-10
**新增工具**：9个智能化工具（4个任务管理 + 1个记忆管理 + 4个可视化）
**测试覆盖**：所有工具通过 n8n 集成测试，支持复杂工作协作场景

### 📋 第四阶段：功能完善 **待开发**
- 📋 插件系统支持
- 📋 更多工具类型
- 📋 性能和稳定性优化

## 📊 当前可用工具列表（总计32个）

### 基础工具（12个）
1. **文件系统工具**
   - `read_file` - 读取文件内容
   - `write_file` - 写入文件内容
   - `list_files` - 列出目录文件
   - `create_directory` - 创建目录

2. **网络工具**
   - `http_request` - HTTP 请求工具
   - `dns_lookup` - DNS 查询工具

3. **搜索工具**
   - `file_search` - 文件名搜索
   - `content_search` - 文件内容搜索

4. **系统工具**
   - `run_command` - 命令执行工具
   - `get_environment` - 环境变量获取
   - `set_working_directory` - 工作目录设置

5. **其他工具**
   - `echo` - 回显测试工具

### 增强工具（6个）
1. **增强文件系统工具**
   - `enhanced_read_file` - 增强文件读取（ChromaDB存储、语言检测、元数据提取）
   - `search_files` - 文件语义搜索（基于ChromaDB）

2. **增强网络工具**
   - `enhanced_fetch_web` - 增强网页获取（ChromaDB存储、文本提取、元数据解析）
   - `search_web_content` - 网页内容语义搜索（基于ChromaDB）

3. **增强系统工具**
   - `get_system_info` - 系统信息获取（ChromaDB存储）
   - `manage_processes` - 进程管理（列出、搜索、查看详情）

### 上下文引擎工具（4个）
1. **代码分析工具**
   - `analyze_code` - 多语言代码分析（支持 Python、JavaScript、TypeScript 等）

2. **智能搜索工具**
   - `search_code` - 智能代码搜索（自然语言查询、语义搜索）
   - `find_similar_code` - 相似代码搜索（基于语义相似度）

3. **项目分析工具**
   - `get_project_overview` - 项目概览统计（文件统计、语言分布、代码结构）

### 任务管理工具（4个）
1. **通用任务管理**
   - `manage_tasks` - 任务创建、更新、删除、查询（基于 ChromaDB）

2. **专门搜索工具**
   - `search_recent_tasks` - 搜索最近创建的任务（按时间排序）
   - `search_tasks_by_time` - 按时间范围搜索任务（支持多种时间范围）
   - `search_tasks_semantic` - 语义搜索任务（基于内容相似性）

### 记忆管理工具（1个）
1. **记忆系统**
   - `manage_memory` - 记忆存储、检索、搜索（支持知识、对话、经验、技能等类型）

### 可视化工具（4个）
1. **图表生成工具**
   - `generate_diagram` - 通用 Mermaid 图表生成（流程图、时序图、思维导图等）
   - `create_data_chart` - 数据图表生成（Chart.js 格式，支持柱状图、折线图、饼图等）

2. **复杂图表工具**
   - `generate_subgraph_diagram` - 复杂嵌套子图生成（支持多模块层次结构）
   - `generate_state_machine` - 状态机图表生成（专门用于自动机和状态转换）

### 工具协作框架（1个）
1. **协作执行器**
   - `execute_tool_chain` - 工具链执行器（支持链式调用、并行执行、数据流传递）

### 核心特性
- **ChromaDB 统一数据存储**：所有增强工具、上下文引擎、任务管理、记忆系统共享统一的向量数据库
- **语义搜索**：基于 sentence-transformers 的智能语义搜索和相似度匹配
- **多语言代码分析**：支持 Python、JavaScript、TypeScript 等多种编程语言
- **智能查询处理**：自然语言查询理解、意图解析、实体提取
- **代码理解能力**：函数、类、导入分析，代码块智能分割，复杂度计算
- **任务管理系统**：基于 ChromaDB 的任务存储、语义搜索、专门搜索工具
- **记忆管理系统**：知识积累、对话历史、经验存储、智能检索
- **可视化能力**：Mermaid 图表、Chart.js 数据图表、复杂嵌套图表、状态机图表
- **工具协作框架**：链式调用、并行执行、数据流传递、复杂工作流支持
- **MCP 协议兼容**：完全兼容 MCP 协议，支持 n8n 等客户端集成
- **智能路径验证**：安全的文件系统访问控制
- **项目级分析**：代码库整体统计、语言分布、结构概览

---

详细的设计文档请参考各个子文档。每个文档都包含具体的功能规格、接口设计、配置选项和使用示例。
