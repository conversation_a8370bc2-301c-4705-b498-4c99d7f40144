# n8n 测试指南 - 第三阶段智能化增强功能

本指南将帮助你在 n8n 中测试刚刚完成的第三阶段功能：任务管理、记忆系统、可视化工具和工具协作。

## 🚀 启动 MCP 服务

### 1. 启动增强模式 MCP 服务

```bash
# 在项目根目录执行
./scripts/run_mcp_daemon.sh
```

服务启动后会显示：
- HTTP地址: http://0.0.0.0:8080
- WebSocket地址: ws://0.0.0.0:8081  
- SSE地址: http://0.0.0.0:8082 (用于n8n连接)

### 2. 验证第三阶段工具已加载

检查日志确认新工具已注册：
```bash
tail -f logs/mcp-toolkit.log
```

应该看到类似输出：
```
任务管理工具注册完成
记忆管理工具注册完成
可视化工具注册完成
工具协作框架注册完成
```

## 📋 第三阶段功能测试

### 1. 任务管理系统测试

#### 测试 1.1：创建任务
在 n8n 中使用 AI Agent 节点，告诉它：

```
请使用任务管理工具创建一个新任务：
- 标题：测试 Python 项目部署
- 描述：在服务器上部署 Python Web 应用
- 优先级：HIGH
- 负责人：开发团队
- 标签：["部署", "Python", "Web应用"]
```

期望结果：
- 成功创建任务并返回任务ID
- 任务信息存储到 ChromaDB
- 返回创建时间和任务详情

#### 测试 1.2：搜索任务
```
搜索与"Python"相关的任务
```

期望结果：
- 返回相关任务列表
- 包含相关性分数
- 显示任务的详细信息

#### 测试 1.3：更新任务状态
```
将刚才创建的任务状态更新为"IN_PROGRESS"，并添加备注
```

### 2. 记忆系统测试

#### 测试 2.1：存储知识记忆
```
请将以下知识存储到记忆系统中：
- 类型：knowledge
- 标题：Docker 最佳实践
- 内容：使用多阶段构建可以显著减少镜像大小，建议在生产环境中使用非root用户运行容器
- 重要性：0.8
- 标签：["Docker", "最佳实践", "安全"]
```

#### 测试 2.2：搜索记忆
```
搜索关于"Docker"的记忆
```

期望结果：
- 返回相关记忆列表
- 按相关性排序
- 显示访问次数和最后访问时间

#### 测试 2.3：存储对话记忆
```
将我们当前的对话存储为对话记忆，标题为"n8n MCP 测试会话"
```

### 3. 可视化工具测试

#### 测试 3.1：生成流程图
```
请生成一个流程图，描述软件开发流程：
开始 -> 需求分析 -> 设计 -> 编码 -> 测试 -> 部署 -> 结束
```

期望结果：
- 返回 Mermaid 代码
- 包含预览 URL
- 支持不同的节点形状

#### 测试 3.2：生成数据图表
```
请创建一个柱状图，显示以下数据：
- 前端开发：45%
- 后端开发：35%
- 数据库：15%
- 运维：5%
```

#### 测试 3.3：自动生成时序图
```
自动生成一个时序图，描述用户登录过程：
用户 -> 前端：输入用户名密码
前端 -> 后端：发送登录请求
后端 -> 数据库：验证用户信息
数据库 -> 后端：返回验证结果
后端 -> 前端：返回登录状态
前端 -> 用户：显示登录结果
```

### 4. 工具协作测试

#### 测试 4.1：顺序执行工具链
```
请执行以下工具链：
1. 首先搜索关于"Python"的记忆
2. 然后基于搜索结果创建一个思维导图
3. 最后将这个思维导图信息存储为新的记忆

使用顺序执行模式，并在步骤间传递数据
```

#### 测试 4.2：并行执行多个任务
```
请并行执行以下操作：
1. 创建一个关于"数据库优化"的任务
2. 存储一个关于"SQL 性能调优"的知识记忆
3. 生成一个数据库架构的流程图

使用并行执行模式
```

#### 测试 4.3：条件执行
```
请执行条件工具链：
1. 搜索关于"测试"的任务
2. 如果找到任务，则创建一个测试报告的可视化图表
3. 如果没有找到，则创建一个新的测试任务

使用条件执行模式
```

## 🔍 高级测试场景

### 场景 1：完整的项目管理工作流
```
模拟一个完整的项目管理场景：
1. 创建项目任务："开发用户认证系统"
2. 存储相关技术知识到记忆系统
3. 生成项目进度甘特图
4. 创建技术架构流程图
5. 将所有信息关联存储

请使用工具协作框架来实现这个完整流程
```

### 场景 2：知识管理和可视化
```
构建一个知识管理系统：
1. 存储多个技术知识点到记忆系统
2. 搜索相关知识并分析
3. 生成知识图谱的思维导图
4. 创建学习路径的流程图

使用工具链来自动化这个过程
```

### 场景 3：任务跟踪和报告
```
实现任务跟踪和报告功能：
1. 创建多个不同优先级的任务
2. 搜索和筛选任务
3. 生成任务状态的饼图
4. 创建项目进度的甘特图
5. 将报告存储为记忆

使用并行和顺序执行的组合
```

## 📊 验证要点

### 功能验证
- ✅ 所有工具都能正常调用
- ✅ ChromaDB 数据正确存储和检索
- ✅ 工具间数据传递正常
- ✅ 可视化图表正确生成
- ✅ 语义搜索返回相关结果

### 性能验证
- ✅ 并行执行比顺序执行更快
- ✅ 大量数据处理不会超时
- ✅ 内存使用在合理范围内
- ✅ 响应时间在可接受范围

### 集成验证
- ✅ n8n Agent 能正确理解工具功能
- ✅ 复杂工具链能正确执行
- ✅ 错误处理和回滚机制正常
- ✅ 日志记录完整清晰

## 🐛 常见问题排查

### 问题 1：工具未找到
**症状**：n8n 提示某个工具不存在
**解决**：检查配置文件中对应工具是否启用，重启 MCP 服务

### 问题 2：ChromaDB 连接失败
**症状**：存储操作失败
**解决**：检查 `mcp_unified_db` 目录权限，确保 ChromaDB 正常初始化

### 问题 3：工具协作执行失败
**症状**：工具链中某个步骤失败
**解决**：检查参数传递格式，确认变量引用语法正确

### 问题 4：可视化图表生成失败
**症状**：Mermaid 代码格式错误
**解决**：检查输入数据格式，使用自动生成模式

## 📝 测试记录模板

```
测试日期：____
测试功能：____
测试结果：✅ 通过 / ❌ 失败
问题描述：____
解决方案：____
备注：____
```

## 🎯 下一步

完成第三阶段测试后，可以：
1. 开始第四阶段高级特性开发
2. 进行生产环境部署准备
3. 优化性能和用户体验
4. 扩展更多专业领域工具
